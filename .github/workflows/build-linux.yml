#
# Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved.
# DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
# This code is free software; you can redistribute it and/or modify it
# under the terms of the GNU General Public License version 2 only, as
# published by the Free Software Foundation.  Oracle designates this
# particular file as subject to the "Classpath" exception as provided
# by Oracle in the LICENSE file that accompanied this code.
#
# This code is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
# FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
# version 2 for more details (a copy is included in the LICENSE file that
# accompanied this code).
#
# You should have received a copy of the GNU General Public License version
# 2 along with this work; if not, write to the Free Software Foundation,
# Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
#
# Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
# or visit www.oracle.com if you need additional information or have any
# questions.
#

name: 'Build (linux)'

on:
  workflow_call:
    inputs:
      platform:
        required: true
        type: string
      extra-conf-options:
        required: false
        type: string
      make-target:
        required: false
        type: string
        default: 'product-bundles test-bundles'
      debug-levels:
        required: false
        type: string
        default: '[ "debug", "release" ]'
      gcc-major-version:
        required: true
        type: string
      gcc-package-suffix:
        required: false
        type: string
        default: ''
      apt-architecture:
        required: false
        type: string
      apt-extra-packages:
        required: false
        type: string
      configure-arguments:
        required: false
        type: string
      make-arguments:
        required: false
        type: string

jobs:
  build-linux:
    name: build
    runs-on: ubuntu-22.04

    strategy:
      fail-fast: false
      matrix:
        debug-level: ${{ fromJSON(inputs.debug-levels) }}
        include:
          - debug-level: debug
            flags: --with-debug-level=fastdebug
            suffix: -debug

    steps:
      - name: 'Checkout the JDK source'
        uses: actions/checkout@v4

      - name: 'Get the BootJDK'
        id: bootjdk
        uses: ./.github/actions/get-bootjdk
        with:
          platform: linux-x64

      - name: 'Get JTReg'
        id: jtreg
        uses: ./.github/actions/get-jtreg

      - name: 'Get GTest'
        id: gtest
        uses: ./.github/actions/get-gtest

      - name: 'Set architecture'
        id: arch
        run: |
          # Set a proper suffix for packages if using a different architecture
          if [[ '${{ inputs.apt-architecture }}' != '' ]]; then
            echo 'suffix=:${{ inputs.apt-architecture }}' >> $GITHUB_OUTPUT
          fi

      # Upgrading apt to solve libc6 installation bugs, see JDK-8260460.
      - name: 'Install toolchain and dependencies'
        run: |
          # Install dependencies using apt-get
          if [[ '${{ inputs.apt-architecture }}' != '' ]]; then
            sudo dpkg --add-architecture ${{ inputs.apt-architecture }}
          fi
          sudo apt-get update
          sudo apt-get install --only-upgrade apt
          sudo apt-get install gcc-${{ inputs.gcc-major-version }}${{ inputs.gcc-package-suffix }} g++-${{ inputs.gcc-major-version }}${{ inputs.gcc-package-suffix }} libxrandr-dev${{ steps.arch.outputs.suffix }} libxtst-dev${{ steps.arch.outputs.suffix }} libcups2-dev${{ steps.arch.outputs.suffix }} libasound2-dev${{ steps.arch.outputs.suffix }} ${{ inputs.apt-extra-packages }}
          sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-${{ inputs.gcc-major-version }} 100 --slave /usr/bin/g++ g++ /usr/bin/g++-${{ inputs.gcc-major-version }}

      - name: 'Configure'
        run: >
          bash configure
          --with-conf-name=${{ inputs.platform }}
          ${{ matrix.flags }}
          --with-version-opt=${GITHUB_ACTOR}-${GITHUB_SHA}
          --with-boot-jdk=${{ steps.bootjdk.outputs.path }}
          --with-jtreg=${{ steps.jtreg.outputs.path }}
          --with-gtest=${{ steps.gtest.outputs.path }}
          --enable-jtreg-failure-handler
          --with-zlib=system
          ${{ inputs.extra-conf-options }} ${{ inputs.configure-arguments }} || (
          echo "Dumping config.log:" &&
          cat config.log &&
          exit 1)

      - name: 'Build'
        id: build
        uses: ./.github/actions/do-build
        with:
          make-target: '${{ inputs.make-target }} ${{ inputs.make-arguments }}'
          platform: ${{ inputs.platform }}
          debug-suffix: '${{ matrix.suffix }}'

      - name: 'Upload bundles'
        uses: ./.github/actions/upload-bundles
        with:
          platform: ${{ inputs.platform }}
          debug-suffix: '${{ matrix.suffix }}'
