#
# Copyright (c) 2001, 2013, Oracle and/or its affiliates. All rights reserved.
# DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
# This code is free software; you can redistribute it and/or modify it
# under the terms of the GNU General Public License version 2 only, as
# published by the Free Software Foundation.  Oracle designates this
# particular file as subject to the "Classpath" exception as provided
# by Oracle in the LICENSE file that accompanied this code.
#
# This code is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
# FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
# version 2 for more details (a copy is included in the LICENSE file that
# accompanied this code).
#
# You should have received a copy of the GNU General Public License version
# 2 along with this work; if not, write to the Free Software Foundation,
# Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
#
# Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
# or visit www.oracle.com if you need additional information or have any
# questions.
#

# Localizations for Level names.  For the US locale
# these are the same as the non-localized level name.

# The following ALL CAPS words should be translated.
ALL=Alle
# The following ALL CAPS words should be translated.
SEVERE=Schwerwiegend
# The following ALL CAPS words should be translated.
WARNING=Warnung
# The following ALL CAPS words should be translated.
INFO=Information
# The following ALL CAPS words should be translated.
CONFIG= Konfiguration
# The following ALL CAPS words should be translated.
FINE=Fein
# The following ALL CAPS words should be translated.
FINER=Feiner
# The following ALL CAPS words should be translated.
FINEST=Am feinsten
# The following ALL CAPS words should be translated.
OFF=Deaktiviert
