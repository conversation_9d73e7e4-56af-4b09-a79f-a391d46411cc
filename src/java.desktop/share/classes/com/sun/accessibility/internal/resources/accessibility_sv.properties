#
# Copyright (c) 2010, 2019, Oracle and/or its affiliates. All rights reserved.
# DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
# This code is free software; you can redistribute it and/or modify it
# under the terms of the GNU General Public License version 2 only, as
# published by the Free Software Foundation.  Oracle designates this
# particular file as subject to the "Classpath" exception as provided
# by Oracle in the LICENSE file that accompanied this code.
#
# This code is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
# FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
# version 2 for more details (a copy is included in the LICENSE file that
# accompanied this code).
#
# You should have received a copy of the GNU General Public License version
# 2 along with this work; if not, write to the Free Software Foundation,
# Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
#
# Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
# or visit www.oracle.com if you need additional information or have any
# questions.
#

#
# This properties file is used to create a PropertyResourceBundle
# It contains Locale specific strings used be the Accessibility package.
#
# When this file is read in, the strings are put into the 
# defaults table.  This is an implementation detail of the current
# workings of Accessibility.  DO NOT DEPEND ON THIS.  
# This may change in future versions of Accessibility as we improve 
# localization support.
#
# <AUTHOR> Monsanto

#
# accessible roles
#
alert=avisering
awtcomponent=AWT-komponent
checkbox=kryssruta
colorchooser=f\u00E4rgv\u00E4ljare
columnheader=kolumnrubrik
combobox=kombinationsruta
canvas=rityta
desktopicon=skrivbordsikon
desktoppane=skrivbordsruta
dialog=dialogruta
directorypane=katalogruta
glasspane=glasruta
filechooser=filv\u00E4ljare
filler=utfyllnad
frame=ram
internalframe=intern ram
label=etikett
layeredpane=staplad ruta
list=lista
listitem=listobjekt
menubar=menyrad
menu=meny
menuitem=menyalternativ
optionpane=alternativruta
pagetab=sidflik
pagetablist=sidflikslista
panel=panel
passwordtext=l\u00F6senordstext
popupmenu=snabbmeny
progressbar=statusrad
pushbutton=knapp
radiobutton=alternativknapp
rootpane=grundruta
rowheader=radrubrik
scrollbar=rullningslist
scrollpane=rullningsruta
separator=avskiljare
slider=skjutreglage
splitpane=delad ruta
swingcomponent=swing-komponent
table=tabell
text=text
tree=tr\u00E4d
togglebutton=v\u00E4xlingsknapp
toolbar=verktygsrad
tooltip=knappbeskrivning
unknown=ok\u00E4nd
viewport=vyport
window=f\u00F6nster
#
# accessible relations
#
labelFor=etikett f\u00F6r
labeledBy=etikett av
memberOf=medlem i
controlledBy=controlledBy
controllerFor=controllerFor
#
# accessible states
#
active=aktiv
armed=redo
busy=upptagen
checked=markerad
collapsed=komprimerad
editable=redigerbar
expandable=ut\u00F6kningsbar
expanded=ut\u00F6kad
enabled=aktiverad
focusable=fokuseringsbar
focused=fokuserad
iconified=minimerad
modal=modal
multiline=flera rader
multiselectable=flerval
opaque=ogenomskinlig
pressed=nedtryckt
resizable=storleks\u00E4ndringsbar
selectable=valbar
selected=vald
showing=visar
singleline=en rad
transient=tillf\u00E4llig
visible=synlig
vertical=vertikal
horizontal=horisontell
#
# accessible actions
#
toggleexpand=v\u00E4xla expandering

# new relations, roles and states for J2SE 1.5.0

# 
# accessible relations
#
flowsTo=fl\u00F6dar till
flowsFrom=fl\u00F6dar fr\u00E5n
subwindowOf=delf\u00F6nster av
parentWindowOf=\u00F6verordnat f\u00F6nster f\u00F6r
embeds=b\u00E4ddar in
embeddedBy=b\u00E4ddas in av
childNodeOf=underordnad nod f\u00F6r

#
# accessible roles
#
header=sidhuvud
footer=sidfot
paragraph=stycke
ruler=linjal
editbar=redigeringslist
progressMonitor=f\u00F6rlopps\u00F6vervakare

#
# accessible states
#
managesDescendants=hanterar underordnade
indeterminate=obest\u00E4mt
truncated=kapad

# new for J2SE 1.6.0

#
# accessible roles
#
htmlcontainer=HTML-container

#
# END OF MATERIAL TO LOCALIZE
#
