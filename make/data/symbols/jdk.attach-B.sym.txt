#
# Copyright (c) 2018, Oracle and/or its affiliates. All rights reserved.
# DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
# This code is free software; you can redistribute it and/or modify it
# under the terms of the GNU General Public License version 2 only, as
# published by the Free Software Foundation.  Oracle designates this
# particular file as subject to the "Classpath" exception as provided
# by <PERSON> in the LICENSE file that accompanied this code.
#
# This code is distributed in the hope that it will be useful, but WITHOUT
# ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
# FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
# version 2 for more details (a copy is included in the LICENSE file that
# accompanied this code).
#
# You should have received a copy of the GNU General Public License version
# 2 along with this work; if not, write to the Free Software Foundation,
# Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
#
# Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
# or visit www.oracle.com if you need additional information or have any
# questions.
#
# ##########################################################
# ### THIS FILE IS AUTOMATICALLY GENERATED. DO NOT EDIT. ###
# ##########################################################
#
module name jdk.attach
header exports com/sun/tools/attach,com/sun/tools/attach/spi requires name\u0020;java.base\u0020;flags\u0020;8000,name\u0020;jdk.internal.jvmstat\u0020;flags\u0020;0 uses com/sun/tools/attach/spi/AttachProvider provides interface\u0020;com/sun/tools/attach/spi/AttachProvider\u0020;impls\u0020;sun/tools/attach/AttachProviderImpl target linux-amd64 flags 8000

class name com/sun/tools/attach/AgentInitializationException
header extends java/lang/Exception flags 21

class name com/sun/tools/attach/AgentLoadException
header extends java/lang/Exception flags 21

class name com/sun/tools/attach/AttachNotSupportedException
header extends java/lang/Exception flags 21

class name com/sun/tools/attach/AttachOperationFailedException
header extends java/io/IOException flags 21

class name com/sun/tools/attach/AttachPermission
header extends java/security/BasicPermission flags 31
innerclass innerClass java/lang/invoke/MethodHandles$Lookup outerClass java/lang/invoke/MethodHandles innerClassName Lookup flags 19

class name com/sun/tools/attach/VirtualMachine
header extends java/lang/Object flags 421
innerclass innerClass java/lang/invoke/MethodHandles$Lookup outerClass java/lang/invoke/MethodHandles innerClassName Lookup flags 19

class name com/sun/tools/attach/VirtualMachineDescriptor
header extends java/lang/Object flags 21
innerclass innerClass java/lang/invoke/MethodHandles$Lookup outerClass java/lang/invoke/MethodHandles innerClassName Lookup flags 19

class name com/sun/tools/attach/spi/AttachProvider
header extends java/lang/Object flags 421

