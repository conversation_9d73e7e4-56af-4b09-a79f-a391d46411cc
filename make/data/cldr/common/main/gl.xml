<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2021 Unicode, Inc.
For terms of use, see http://www.unicode.org/copyright.html
Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)

Warnings: All cp values have U+FE0F characters removed. See /annotationsDerived/ for derived annotations.
-->
<ldml>
	<identity>
		<version number="$Revision$"/>
		<language type="gl"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="aa">afar</language>
			<language type="ab">abkhazo</language>
			<language type="ace">achinés</language>
			<language type="ach">acholí</language>
			<language type="ada">adangme</language>
			<language type="ady">adigueo</language>
			<language type="af">afrikaans</language>
			<language type="agq">aghem</language>
			<language type="ain">ainu</language>
			<language type="ak">akan</language>
			<language type="ale">aleutiano</language>
			<language type="alt">altai meridional</language>
			<language type="am">amhárico</language>
			<language type="an">aragonés</language>
			<language type="anp">angika</language>
			<language type="ar">árabe</language>
			<language type="ar_001">árabe estándar moderno</language>
			<language type="arc">arameo</language>
			<language type="arn">mapuche</language>
			<language type="arp">arapaho</language>
			<language type="as">assamés</language>
			<language type="asa">asu</language>
			<language type="ast">asturiano</language>
			<language type="av">avar</language>
			<language type="awa">awadhi</language>
			<language type="ay">aimará</language>
			<language type="az">acerbaixano</language>
			<language type="az" alt="short">azerí</language>
			<language type="ba">baxkir</language>
			<language type="ban">balinés</language>
			<language type="bas">basaa</language>
			<language type="be">belaruso</language>
			<language type="bem">bemba</language>
			<language type="bez">bena</language>
			<language type="bg">búlgaro</language>
			<language type="bgn">baluchi occidental</language>
			<language type="bho">bhojpuri</language>
			<language type="bi">bislama</language>
			<language type="bin">bini</language>
			<language type="bla">siksiká</language>
			<language type="bm">bambara</language>
			<language type="bn">bengalí</language>
			<language type="bo">tibetano</language>
			<language type="br">bretón</language>
			<language type="brx">bodo</language>
			<language type="bs">bosníaco</language>
			<language type="bug">buginés</language>
			<language type="byn">blin</language>
			<language type="ca">catalán</language>
			<language type="ccp">chakma</language>
			<language type="ce">checheno</language>
			<language type="ceb">cebuano</language>
			<language type="cgg">kiga</language>
			<language type="ch">chamorro</language>
			<language type="chk">chuuk</language>
			<language type="chm">mari</language>
			<language type="cho">choctaw</language>
			<language type="chr">cherokee</language>
			<language type="chy">cheyenne</language>
			<language type="ckb">kurdo central</language>
			<language type="ckb" alt="variant">sorani</language>
			<language type="co">corso</language>
			<language type="crs">seselwa (crioulo das Seychelles)</language>
			<language type="cs">checo</language>
			<language type="cu">eslavo eclesiástico</language>
			<language type="cv">chuvaxo</language>
			<language type="cy">galés</language>
			<language type="da">dinamarqués</language>
			<language type="dak">dakota</language>
			<language type="dar">dargwa</language>
			<language type="dav">taita</language>
			<language type="de">alemán</language>
			<language type="de_AT">alemán austríaco</language>
			<language type="de_CH">alto alemán suízo</language>
			<language type="dgr">dogrib</language>
			<language type="dje">zarma</language>
			<language type="dsb">baixo sorbio</language>
			<language type="dua">duala</language>
			<language type="dv">divehi</language>
			<language type="dyo">jola-fonyi</language>
			<language type="dz">dzongkha</language>
			<language type="dzg">dazaga</language>
			<language type="ebu">embu</language>
			<language type="ee">ewe</language>
			<language type="efi">efik</language>
			<language type="egy">exipcio antigo</language>
			<language type="eka">ekajuk</language>
			<language type="el">grego</language>
			<language type="en">inglés</language>
			<language type="en_AU">inglés australiano</language>
			<language type="en_CA">inglés canadense</language>
			<language type="en_GB">inglés británico</language>
			<language type="en_US">inglés estadounidense</language>
			<language type="eo">esperanto</language>
			<language type="es">español</language>
			<language type="es_419">español de América</language>
			<language type="es_ES">español de España</language>
			<language type="es_MX">español de México</language>
			<language type="et">estoniano</language>
			<language type="eu">éuscaro</language>
			<language type="ewo">ewondo</language>
			<language type="fa">persa</language>
			<language type="fa_AF">dari</language>
			<language type="ff">fula</language>
			<language type="fi">finés</language>
			<language type="fil">filipino</language>
			<language type="fj">fixiano</language>
			<language type="fo">feroés</language>
			<language type="fon">fon</language>
			<language type="fr">francés</language>
			<language type="fr_CA">francés canadense</language>
			<language type="fr_CH">francés suízo</language>
			<language type="fur">friulano</language>
			<language type="fy">frisón occidental</language>
			<language type="ga">irlandés</language>
			<language type="gaa">ga</language>
			<language type="gag">gagauz</language>
			<language type="gd">gaélico escocés</language>
			<language type="gez">ge’ez</language>
			<language type="gil">kiribatiano</language>
			<language type="gl">galego</language>
			<language type="gn">guaraní</language>
			<language type="gor">gorontalo</language>
			<language type="grc">grego antigo</language>
			<language type="gsw">alemán suízo</language>
			<language type="gu">guxarati</language>
			<language type="guz">gusii</language>
			<language type="gv">manx</language>
			<language type="gwi">gwichʼin</language>
			<language type="ha">hausa</language>
			<language type="haw">hawaiano</language>
			<language type="he">hebreo</language>
			<language type="hi">hindi</language>
			<language type="hil">hiligaynon</language>
			<language type="hmn">hmong</language>
			<language type="hr">croata</language>
			<language type="hsb">alto sorbio</language>
			<language type="ht">crioulo haitiano</language>
			<language type="hu">húngaro</language>
			<language type="hup">hupa</language>
			<language type="hy">armenio</language>
			<language type="hz">herero</language>
			<language type="ia">interlingua</language>
			<language type="iba">iban</language>
			<language type="ibb">ibibio</language>
			<language type="id">indonesio</language>
			<language type="ig">igbo</language>
			<language type="ii">yi sichuanés</language>
			<language type="ilo">ilocano</language>
			<language type="inh">inguxo</language>
			<language type="io">ido</language>
			<language type="is">islandés</language>
			<language type="it">italiano</language>
			<language type="iu">inuktitut</language>
			<language type="ja">xaponés</language>
			<language type="jbo">lojban</language>
			<language type="jgo">ngomba</language>
			<language type="jmc">machame</language>
			<language type="jv">xavanés</language>
			<language type="ka">xeorxiano</language>
			<language type="kab">cabila</language>
			<language type="kac">kachin</language>
			<language type="kaj">jju</language>
			<language type="kam">kamba</language>
			<language type="kbd">cabardiano</language>
			<language type="kcg">tyap</language>
			<language type="kde">makonde</language>
			<language type="kea">caboverdiano</language>
			<language type="kfo">koro</language>
			<language type="kg">kongo</language>
			<language type="kha">khasi</language>
			<language type="khq">koyra chiini</language>
			<language type="ki">kikuyu</language>
			<language type="kj">kuanyama</language>
			<language type="kk">kazako</language>
			<language type="kkj">kako</language>
			<language type="kl">groenlandés</language>
			<language type="kln">kalenjin</language>
			<language type="km">khmer</language>
			<language type="kmb">kimbundu</language>
			<language type="kn">kannará</language>
			<language type="ko">coreano</language>
			<language type="koi">komi permio</language>
			<language type="kok">konkani</language>
			<language type="kpe">kpelle</language>
			<language type="kr">kanuri</language>
			<language type="krc">carachaio-bálcara</language>
			<language type="krl">carelio</language>
			<language type="kru">kurukh</language>
			<language type="ks">caxemirés</language>
			<language type="ksb">shambala</language>
			<language type="ksf">bafia</language>
			<language type="ksh">kölsch</language>
			<language type="ku">kurdo</language>
			<language type="kum">kumyk</language>
			<language type="kv">komi</language>
			<language type="kw">córnico</language>
			<language type="ky">kirguiz</language>
			<language type="la">latín</language>
			<language type="lad">ladino</language>
			<language type="lag">langi</language>
			<language type="lb">luxemburgués</language>
			<language type="lez">lezguio</language>
			<language type="lg">ganda</language>
			<language type="li">limburgués</language>
			<language type="lkt">lakota</language>
			<language type="ln">lingala</language>
			<language type="lo">laosiano</language>
			<language type="loz">lozi</language>
			<language type="lrc">luri setentrional</language>
			<language type="lt">lituano</language>
			<language type="lu">luba-katanga</language>
			<language type="lua">luba-lulua</language>
			<language type="lun">lunda</language>
			<language type="luo">luo</language>
			<language type="lus">mizo</language>
			<language type="luy">luyia</language>
			<language type="lv">letón</language>
			<language type="mad">madurés</language>
			<language type="mag">magahi</language>
			<language type="mai">maithili</language>
			<language type="mak">makasar</language>
			<language type="mas">masai</language>
			<language type="mdf">moksha</language>
			<language type="men">mende</language>
			<language type="mer">meru</language>
			<language type="mfe">crioulo mauriciano</language>
			<language type="mg">malgaxe</language>
			<language type="mgh">makhuwa-meetto</language>
			<language type="mgo">meta’</language>
			<language type="mh">marshalés</language>
			<language type="mi">maorí</language>
			<language type="mic">micmac</language>
			<language type="min">minangkabau</language>
			<language type="mk">macedonio</language>
			<language type="ml">malabar</language>
			<language type="mn">mongol</language>
			<language type="mni">manipuri</language>
			<language type="moh">mohawk</language>
			<language type="mos">mossi</language>
			<language type="mr">marathi</language>
			<language type="ms">malaio</language>
			<language type="mt">maltés</language>
			<language type="mua">mundang</language>
			<language type="mul">varias linguas</language>
			<language type="mus">creek</language>
			<language type="mwl">mirandés</language>
			<language type="my">birmano</language>
			<language type="myv">erzya</language>
			<language type="mzn">mazandaraní</language>
			<language type="na">nauruano</language>
			<language type="nap">napolitano</language>
			<language type="naq">nama</language>
			<language type="nb">noruegués bokmål</language>
			<language type="nd">ndebele setentrional</language>
			<language type="nds">baixo alemán</language>
			<language type="nds_NL">baixo saxón</language>
			<language type="ne">nepalí</language>
			<language type="new">newari</language>
			<language type="ng">ndonga</language>
			<language type="nia">nias</language>
			<language type="niu">niueano</language>
			<language type="nl">neerlandés</language>
			<language type="nl_BE">flamengo</language>
			<language type="nmg">kwasio</language>
			<language type="nn">noruegués nynorsk</language>
			<language type="nnh">ngiemboon</language>
			<language type="no">noruegués</language>
			<language type="nog">nogai</language>
			<language type="nqo">n’ko</language>
			<language type="nr">ndebele meridional</language>
			<language type="nso">sesotho do norte</language>
			<language type="nus">nuer</language>
			<language type="nv">navajo</language>
			<language type="ny">chewa</language>
			<language type="nyn">nyankole</language>
			<language type="oc">occitano</language>
			<language type="om">oromo</language>
			<language type="or">odiá</language>
			<language type="os">ossetio</language>
			<language type="pa">panxabí</language>
			<language type="pag">pangasinan</language>
			<language type="pam">pampanga</language>
			<language type="pap">papiamento</language>
			<language type="pau">palauano</language>
			<language type="pcm">pidgin nixeriano</language>
			<language type="pl">polaco</language>
			<language type="prg">prusiano</language>
			<language type="ps">paxto</language>
			<language type="pt">portugués</language>
			<language type="pt_BR">portugués do Brasil</language>
			<language type="pt_PT">portugués de Portugal</language>
			<language type="qu">quechua</language>
			<language type="quc">quiché</language>
			<language type="rap">rapanui</language>
			<language type="rar">rarotongano</language>
			<language type="rm">romanche</language>
			<language type="rn">rundi</language>
			<language type="ro">romanés</language>
			<language type="ro_MD">moldavo</language>
			<language type="rof">rombo</language>
			<language type="ru">ruso</language>
			<language type="rup">aromanés</language>
			<language type="rw">kiñaruanda</language>
			<language type="rwk">rwa</language>
			<language type="sa">sánscrito</language>
			<language type="sad">sandawe</language>
			<language type="sah">iacuto</language>
			<language type="saq">samburu</language>
			<language type="sat">santali</language>
			<language type="sba">ngambay</language>
			<language type="sbp">sangu</language>
			<language type="sc">sardo</language>
			<language type="scn">siciliano</language>
			<language type="sco">escocés</language>
			<language type="sd">sindhi</language>
			<language type="sdh">kurdo meridional</language>
			<language type="se">saami setentrional</language>
			<language type="seh">sena</language>
			<language type="ses">koyraboro senni</language>
			<language type="sg">sango</language>
			<language type="sh">serbocroata</language>
			<language type="shi">tachelhit</language>
			<language type="shn">shan</language>
			<language type="si">cingalés</language>
			<language type="sk">eslovaco</language>
			<language type="sl">esloveno</language>
			<language type="sm">samoano</language>
			<language type="sma">saami meridional</language>
			<language type="smj">saami de Lule</language>
			<language type="smn">saami de Inari</language>
			<language type="sms">saami skolt</language>
			<language type="sn">shona</language>
			<language type="snk">soninke</language>
			<language type="so">somalí</language>
			<language type="sq">albanés</language>
			<language type="sr">serbio</language>
			<language type="srn">sranan tongo</language>
			<language type="ss">suazi</language>
			<language type="ssy">saho</language>
			<language type="st">sesotho</language>
			<language type="su">sundanés</language>
			<language type="suk">sukuma</language>
			<language type="sv">sueco</language>
			<language type="sw">suahili</language>
			<language type="sw_CD">suahili congolés</language>
			<language type="swb">comoriano</language>
			<language type="syr">siríaco</language>
			<language type="ta">támil</language>
			<language type="te">telugu</language>
			<language type="tem">temne</language>
			<language type="teo">teso</language>
			<language type="tet">tetun</language>
			<language type="tg">taxico</language>
			<language type="th">tailandés</language>
			<language type="ti">tigriña</language>
			<language type="tig">tigré</language>
			<language type="tk">turkmeno</language>
			<language type="tl">tagalo</language>
			<language type="tlh">klingon</language>
			<language type="tn">tswana</language>
			<language type="to">tongano</language>
			<language type="tpi">tok pisin</language>
			<language type="tr">turco</language>
			<language type="trv">taroko</language>
			<language type="ts">tsonga</language>
			<language type="tt">tártaro</language>
			<language type="tum">tumbuka</language>
			<language type="tvl">tuvalés</language>
			<language type="tw">twi</language>
			<language type="twq">tasawaq</language>
			<language type="ty">tahitiano</language>
			<language type="tyv">tuvaniano</language>
			<language type="tzm">tamazight de Marrocos central</language>
			<language type="udm">udmurto</language>
			<language type="ug">uigur</language>
			<language type="uk">ucraíno</language>
			<language type="umb">umbundu</language>
			<language type="und">lingua descoñecida</language>
			<language type="ur">urdú</language>
			<language type="uz">uzbeko</language>
			<language type="vai">vai</language>
			<language type="ve">venda</language>
			<language type="vi">vietnamita</language>
			<language type="vo">volapuk</language>
			<language type="vun">vunjo</language>
			<language type="wa">valón</language>
			<language type="wae">walser</language>
			<language type="wal">wolaytta</language>
			<language type="war">waray-waray</language>
			<language type="wbp">walrpiri</language>
			<language type="wo">wólof</language>
			<language type="xal">calmuco</language>
			<language type="xh">xhosa</language>
			<language type="xog">soga</language>
			<language type="yav">yangben</language>
			<language type="ybb">yemba</language>
			<language type="yi">yiddish</language>
			<language type="yo">ioruba</language>
			<language type="yue">cantonés</language>
			<language type="yue" alt="menu">chinés cantonés</language>
			<language type="zgh">tamazight marroquí estándar</language>
			<language type="zh">chinés</language>
			<language type="zh" alt="menu">chinés mandarín</language>
			<language type="zh_Hans">chinés simplificado</language>
			<language type="zh_Hans" alt="long">chinés mandarín simplificado</language>
			<language type="zh_Hant">chinés tradicional</language>
			<language type="zh_Hant" alt="long">chinés mandarín tradicional</language>
			<language type="zu">zulú</language>
			<language type="zun">zuni</language>
			<language type="zxx">sen contido lingüístico</language>
			<language type="zza">zazaki</language>
		</languages>
		<scripts>
			<script type="Arab">árabe</script>
			<script type="Arab" alt="variant">perso-árabe</script>
			<script type="Armn">armenio</script>
			<script type="Beng">bengalí</script>
			<script type="Bopo">bopomofo</script>
			<script type="Brai">braille</script>
			<script type="Cans">Silabario aborixe canadiano unificado</script>
			<script type="Cyrl">cirílico</script>
			<script type="Deva">devanágari</script>
			<script type="Ethi">etíope</script>
			<script type="Geor">xeorxiano</script>
			<script type="Grek">grego</script>
			<script type="Gujr">guxarati</script>
			<script type="Guru">gurmukhi</script>
			<script type="Hanb">han con bopomofo</script>
			<script type="Hang">hangul</script>
			<script type="Hani">han</script>
			<script type="Hans">simplificado</script>
			<script type="Hans" alt="stand-alone">han simplificado</script>
			<script type="Hant">tradicional</script>
			<script type="Hant" alt="stand-alone">han tradicional</script>
			<script type="Hebr">hebreo</script>
			<script type="Hira">hiragana</script>
			<script type="Hrkt">silabarios xaponeses</script>
			<script type="Jamo">jamo</script>
			<script type="Jpan">xaponés</script>
			<script type="Kana">katakana</script>
			<script type="Khmr">khmer</script>
			<script type="Knda">kannará</script>
			<script type="Kore">coreano</script>
			<script type="Laoo">laosiano</script>
			<script type="Latn">latino</script>
			<script type="Mlym">malabar</script>
			<script type="Mong">mongol</script>
			<script type="Mymr">birmano</script>
			<script type="Orya">odiá</script>
			<script type="Sinh">cingalés</script>
			<script type="Taml">támil</script>
			<script type="Telu">telugu</script>
			<script type="Thaa">thaana</script>
			<script type="Thai">tailandés</script>
			<script type="Tibt">tibetano</script>
			<script type="Zmth">notación matemática</script>
			<script type="Zsye">emojis</script>
			<script type="Zsym">símbolos</script>
			<script type="Zxxx">non escrito</script>
			<script type="Zyyy">común</script>
			<script type="Zzzz">sistema de escritura descoñecido</script>
		</scripts>
		<territories>
			<territory type="001">Mundo</territory>
			<territory type="002">África</territory>
			<territory type="003">América do Norte</territory>
			<territory type="005">América do Sur</territory>
			<territory type="009">Oceanía</territory>
			<territory type="011">África Occidental</territory>
			<territory type="013">América Central</territory>
			<territory type="014">África Oriental</territory>
			<territory type="015">África Setentrional</territory>
			<territory type="017">África Central</territory>
			<territory type="018">África Meridional</territory>
			<territory type="019">América</territory>
			<territory type="021">América Setentrional</territory>
			<territory type="029">Caribe</territory>
			<territory type="030">Asia Oriental</territory>
			<territory type="034">Asia Meridional</territory>
			<territory type="035">Sueste Asiático</territory>
			<territory type="039">Europa Meridional</territory>
			<territory type="053">Australasia</territory>
			<territory type="054">Melanesia</territory>
			<territory type="057">Rexión de Micronesia</territory>
			<territory type="061">Polinesia</territory>
			<territory type="142">Asia</territory>
			<territory type="143">Asia Central</territory>
			<territory type="145">Asia Occidental</territory>
			<territory type="150">Europa</territory>
			<territory type="151">Europa do Leste</territory>
			<territory type="154">Europa Setentrional</territory>
			<territory type="155">Europa Occidental</territory>
			<territory type="202">África subsahariana</territory>
			<territory type="419">América Latina</territory>
			<territory type="AC">Illa de Ascensión</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">Os Emiratos Árabes Unidos</territory>
			<territory type="AF">Afganistán</territory>
			<territory type="AG">Antigua e Barbuda</territory>
			<territory type="AI">Anguila</territory>
			<territory type="AL">Albania</territory>
			<territory type="AM">Armenia</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">A Antártida</territory>
			<territory type="AR">A Arxentina</territory>
			<territory type="AS">Samoa Americana</territory>
			<territory type="AT">Austria</territory>
			<territory type="AU">Australia</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Illas Åland</territory>
			<territory type="AZ">Acerbaixán</territory>
			<territory type="BA">Bosnia e Hercegovina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesh</territory>
			<territory type="BE">Bélxica</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bulgaria</territory>
			<territory type="BH">Bahrain</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benín</territory>
			<territory type="BL">Saint Barthélemy</territory>
			<territory type="BM">Illas Bermudas</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolivia</territory>
			<territory type="BQ">Caribe Neerlandés</territory>
			<territory type="BR">O Brasil</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Bután</territory>
			<territory type="BV">Illa Bouvet</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Belarús</territory>
			<territory type="BZ">Belize</territory>
			<territory type="CA">O Canadá</territory>
			<territory type="CC">Illas Cocos (Keeling)</territory>
			<territory type="CD">República Democrática do Congo</territory>
			<territory type="CD" alt="variant">Congo (RDC)</territory>
			<territory type="CF">República Centroafricana</territory>
			<territory type="CG">República do Congo</territory>
			<territory type="CG" alt="variant">Congo (RC)</territory>
			<territory type="CH">Suíza</territory>
			<territory type="CI">Côte d’Ivoire</territory>
			<territory type="CI" alt="variant">Costa do Marfil</territory>
			<territory type="CK">Illas Cook</territory>
			<territory type="CL">Chile</territory>
			<territory type="CM">Camerún</territory>
			<territory type="CN">A China</territory>
			<territory type="CO">Colombia</territory>
			<territory type="CP">Illa Clipperton</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Cuba</territory>
			<territory type="CV">Cabo Verde</territory>
			<territory type="CW">Curaçao</territory>
			<territory type="CX">Illa Christmas</territory>
			<territory type="CY">Chipre</territory>
			<territory type="CZ">Chequia</territory>
			<territory type="CZ" alt="variant">República Checa</territory>
			<territory type="DE">Alemaña</territory>
			<territory type="DG">Diego García</territory>
			<territory type="DJ">Djibuti</territory>
			<territory type="DK">Dinamarca</territory>
			<territory type="DM">Dominica</territory>
			<territory type="DO">República Dominicana</territory>
			<territory type="DZ">Alxeria</territory>
			<territory type="EA">Ceuta e Melilla</territory>
			<territory type="EC">Ecuador</territory>
			<territory type="EE">Estonia</territory>
			<territory type="EG">Exipto</territory>
			<territory type="EH">O Sáhara Occidental</territory>
			<territory type="ER">Eritrea</territory>
			<territory type="ES">España</territory>
			<territory type="ET">Etiopía</territory>
			<territory type="EU">Unión Europea</territory>
			<territory type="EZ">Eurozona</territory>
			<territory type="FI">Finlandia</territory>
			<territory type="FJ">Fixi</territory>
			<territory type="FK">Illas Malvinas</territory>
			<territory type="FK" alt="variant">Illas Malvinas (Falkland)</territory>
			<territory type="FM">Micronesia</territory>
			<territory type="FO">Illas Feroe</territory>
			<territory type="FR">Francia</territory>
			<territory type="GA">Gabón</territory>
			<territory type="GB">O Reino Unido</territory>
			<territory type="GB" alt="short">RU</territory>
			<territory type="GD">Granada</territory>
			<territory type="GE">Xeorxia</territory>
			<territory type="GF">Güiana Francesa</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Ghana</territory>
			<territory type="GI">Xibraltar</territory>
			<territory type="GL">Groenlandia</territory>
			<territory type="GM">Gambia</territory>
			<territory type="GN">Guinea</territory>
			<territory type="GP">Guadalupe</territory>
			<territory type="GQ">Guinea Ecuatorial</territory>
			<territory type="GR">Grecia</territory>
			<territory type="GS">Illas Xeorxia do Sur e Sandwich do Sur</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">A Guinea Bissau</territory>
			<territory type="GY">Güiana</territory>
			<territory type="HK">Hong Kong RAE da China</territory>
			<territory type="HK" alt="short">Hong Kong</territory>
			<territory type="HM">Illa Heard e Illas McDonald</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Croacia</territory>
			<territory type="HT">Haití</territory>
			<territory type="HU">Hungría</territory>
			<territory type="IC">Illas Canarias</territory>
			<territory type="ID">Indonesia</territory>
			<territory type="IE">Irlanda</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Illa de Man</territory>
			<territory type="IN">A India</territory>
			<territory type="IO">Territorio Británico do Océano Índico</territory>
			<territory type="IQ">Iraq</territory>
			<territory type="IR">Irán</territory>
			<territory type="IS">Islandia</territory>
			<territory type="IT">Italia</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Xamaica</territory>
			<territory type="JO">Xordania</territory>
			<territory type="JP">O Xapón</territory>
			<territory type="KE">Kenya</territory>
			<territory type="KG">Kirguizistán</territory>
			<territory type="KH">Camboxa</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Comores</territory>
			<territory type="KN">Saint Kitts e Nevis</territory>
			<territory type="KP">Corea do Norte</territory>
			<territory type="KR">Corea do Sur</territory>
			<territory type="KW">Kuwait</territory>
			<territory type="KY">Illas Caimán</territory>
			<territory type="KZ">Kazakistán</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">O Líbano</territory>
			<territory type="LC">Santa Lucía</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberia</territory>
			<territory type="LS">Lesotho</territory>
			<territory type="LT">Lituania</territory>
			<territory type="LU">Luxemburgo</territory>
			<territory type="LV">Letonia</territory>
			<territory type="LY">Libia</territory>
			<territory type="MA">Marrocos</territory>
			<territory type="MC">Mónaco</territory>
			<territory type="MD">Moldavia</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">Saint Martin</territory>
			<territory type="MG">Madagascar</territory>
			<territory type="MH">Illas Marshall</territory>
			<territory type="MK">Macedonia do Norte</territory>
			<territory type="ML">Malí</territory>
			<territory type="MM">Myanmar (Birmania)</territory>
			<territory type="MN">Mongolia</territory>
			<territory type="MO">Macau RAE da China</territory>
			<territory type="MO" alt="short">Macau</territory>
			<territory type="MP">Illas Marianas do Norte</territory>
			<territory type="MQ">Martinica</territory>
			<territory type="MR">Mauritania</territory>
			<territory type="MS">Montserrat</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Mauricio</territory>
			<territory type="MV">Maldivas</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">México</territory>
			<territory type="MY">Malaisia</territory>
			<territory type="MZ">Mozambique</territory>
			<territory type="NA">Namibia</territory>
			<territory type="NC">Nova Caledonia</territory>
			<territory type="NE">Níxer</territory>
			<territory type="NF">Illa Norfolk</territory>
			<territory type="NG">Nixeria</territory>
			<territory type="NI">Nicaragua</territory>
			<territory type="NL">Países Baixos</territory>
			<territory type="NO">Noruega</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">Nova Zelandia</territory>
			<territory type="OM">Omán</territory>
			<territory type="PA">Panamá</territory>
			<territory type="PE">O Perú</territory>
			<territory type="PF">A Polinesia Francesa</territory>
			<territory type="PG">Papúa-Nova Guinea</territory>
			<territory type="PH">Filipinas</territory>
			<territory type="PK">Paquistán</territory>
			<territory type="PL">Polonia</territory>
			<territory type="PM">Saint Pierre et Miquelon</territory>
			<territory type="PN">Illas Pitcairn</territory>
			<territory type="PR">Porto Rico</territory>
			<territory type="PS">Territorios Palestinos</territory>
			<territory type="PS" alt="short">Palestina</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">O Paraguai</territory>
			<territory type="QA">Qatar</territory>
			<territory type="QO">Territorios afastados de Oceanía</territory>
			<territory type="RE">Reunión</territory>
			<territory type="RO">Romanía</territory>
			<territory type="RS">Serbia</territory>
			<territory type="RU">Rusia</territory>
			<territory type="RW">Ruanda</territory>
			<territory type="SA">Arabia Saudita</territory>
			<territory type="SB">Illas Salomón</territory>
			<territory type="SC">Seychelles</territory>
			<territory type="SD">O Sudán</territory>
			<territory type="SE">Suecia</territory>
			<territory type="SG">Singapur</territory>
			<territory type="SH">Santa Helena</territory>
			<territory type="SI">Eslovenia</territory>
			<territory type="SJ">Svalbard e Jan Mayen</territory>
			<territory type="SK">Eslovaquia</territory>
			<territory type="SL">Serra Leoa</territory>
			<territory type="SM">San Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somalia</territory>
			<territory type="SR">Suriname</territory>
			<territory type="SS">O Sudán do Sur</territory>
			<territory type="ST">San Tomé e Príncipe</territory>
			<territory type="SV">O Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Siria</territory>
			<territory type="SZ">Eswatini</territory>
			<territory type="SZ" alt="variant">Swazilandia</territory>
			<territory type="TA">Tristán da Cunha</territory>
			<territory type="TC">Illas Turks e Caicos</territory>
			<territory type="TD">Chad</territory>
			<territory type="TF">Territorios Austrais Franceses</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Tailandia</territory>
			<territory type="TJ">Taxiquistán</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Timor Leste</territory>
			<territory type="TM">Turkmenistán</territory>
			<territory type="TN">Tunisia</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turquía</territory>
			<territory type="TT">Trinidad e Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwán</territory>
			<territory type="TZ">Tanzania</territory>
			<territory type="UA">Ucraína</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">Illas Menores Distantes dos Estados Unidos</territory>
			<territory type="UN">Nacións Unidas</territory>
			<territory type="UN" alt="short">ONU</territory>
			<territory type="US">Os Estados Unidos</territory>
			<territory type="US" alt="short">EUA</territory>
			<territory type="UY">O Uruguai</territory>
			<territory type="UZ">Uzbekistán</territory>
			<territory type="VA">Cidade do Vaticano</territory>
			<territory type="VC">San Vicente e As Granadinas</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Illas Virxes Británicas</territory>
			<territory type="VI">Illas Virxes Estadounidenses</territory>
			<territory type="VN">Vietnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis e Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XA">Pseudoacentos</territory>
			<territory type="XB">Pseudobidireccional</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">O Iemen</territory>
			<territory type="YT">Mayotte</territory>
			<territory type="ZA">Suráfrica</territory>
			<territory type="ZM">Zambia</territory>
			<territory type="ZW">Zimbabwe</territory>
			<territory type="ZZ">Rexión descoñecida</territory>
		</territories>
		<keys>
			<key type="calendar">calendario</key>
			<key type="cf">formato de moeda</key>
			<key type="colAlternate">ignorar ordenación de símbolos</key>
			<key type="colBackwards">ordenación de acentos invertida</key>
			<key type="colCaseFirst">orde de maiúsculas/minúsculas</key>
			<key type="colCaseLevel">ordenación que distingue entre maiúsculas e minúsculas</key>
			<key type="collation">criterio de ordenación</key>
			<key type="colNormalization">ordenación normalizada</key>
			<key type="colNumeric">ordenación numérica</key>
			<key type="colStrength">forza de ordenación</key>
			<key type="currency">moeda</key>
			<key type="hc">ciclo horario (12 ou 24)</key>
			<key type="lb">estilo de quebra de liña</key>
			<key type="ms">sistema internacional de unidades</key>
			<key type="numbers">números</key>
			<key type="timezone">fuso horario</key>
			<key type="va">variante rexional</key>
			<key type="x">uso privado</key>
		</keys>
		<types>
			<type key="calendar" type="buddhist">calendario budista</type>
			<type key="calendar" type="chinese">calendario chinés</type>
			<type key="calendar" type="coptic">Calendario cóptico</type>
			<type key="calendar" type="dangi">calendario dangi</type>
			<type key="calendar" type="ethiopic">calendario etíope</type>
			<type key="calendar" type="ethiopic-amete-alem">Calendario Amete Alem etíope</type>
			<type key="calendar" type="gregorian">calendario gregoriano</type>
			<type key="calendar" type="hebrew">calendario hebreo</type>
			<type key="calendar" type="indian">Calendario nacional indio</type>
			<type key="calendar" type="islamic">calendario islámico</type>
			<type key="calendar" type="islamic-civil">Calendario islámico (civil, tabular)</type>
			<type key="calendar" type="islamic-rgsa">Calendario islámico (Arabia Saudita,</type>
			<type key="calendar" type="iso8601">calendario ISO-8601</type>
			<type key="calendar" type="japanese">calendario xaponés</type>
			<type key="calendar" type="persian">calendario persa</type>
			<type key="calendar" type="roc">calendario Minguo</type>
			<type key="cf" type="account">formato de moeda contable</type>
			<type key="cf" type="standard">formato de moeda estándar</type>
			<type key="colAlternate" type="non-ignorable">Clasificar símbolos</type>
			<type key="colAlternate" type="shifted">Clasificar ignorando símbolos</type>
			<type key="colBackwards" type="no">Clasificar acentos con normalidade</type>
			<type key="colBackwards" type="yes">Clasificar acentos invertidos</type>
			<type key="colCaseFirst" type="lower">Clasificar primeiro as minúsculas</type>
			<type key="colCaseFirst" type="no">Clasificar orde de maiúsculas e minúsculas normal</type>
			<type key="colCaseFirst" type="upper">Clasificar primeiro as maiúsculas</type>
			<type key="colCaseLevel" type="no">Clasificar sen distinguir entre maiúsculas e minúsculas</type>
			<type key="colCaseLevel" type="yes">Clasificar distinguindo entre maiúsculas e minúsculas</type>
			<type key="collation" type="big5han">Orde de clasificación chinesa tradicional - Big5</type>
			<type key="collation" type="dictionary">Criterio de ordenación do dicionario</type>
			<type key="collation" type="ducet">criterio de ordenación Unicode predeterminado</type>
			<type key="collation" type="gb2312han">orde de clasifcación chinesa simplificada - GB2312</type>
			<type key="collation" type="phonebook">orde de clasificación da guía telefónica</type>
			<type key="collation" type="phonetic">Orde de clasificación fonética</type>
			<type key="collation" type="pinyin">Orde de clasificación pinyin</type>
			<type key="collation" type="reformed">Criterio de ordenación reformado</type>
			<type key="collation" type="search">busca de carácter xeral</type>
			<type key="collation" type="searchjl">Clasificar por consonante inicial hangul</type>
			<type key="collation" type="standard">criterio de ordenación estándar</type>
			<type key="collation" type="stroke">Orde de clasificación polo número de trazos</type>
			<type key="collation" type="traditional">Orde de clasificación tradicional</type>
			<type key="collation" type="unihan">Criterio de ordenación radical-trazo</type>
			<type key="colNormalization" type="no">Clasificar sen normalización</type>
			<type key="colNormalization" type="yes">Clasificar Unicode normalizado</type>
			<type key="colNumeric" type="no">Clasificar díxitos individualmente</type>
			<type key="colNumeric" type="yes">Clasificar díxitos numericamente</type>
			<type key="colStrength" type="identical">Clasificar todo</type>
			<type key="colStrength" type="primary">Clasificar só letras de base</type>
			<type key="colStrength" type="quaternary">Clasificar acentos/maiúsculas e minúsculas/ancho/kana</type>
			<type key="colStrength" type="secondary">Clasificar acentos</type>
			<type key="colStrength" type="tertiary">Clasificar acentos/maiúsculas e minúsculas/ancho</type>
			<type key="d0" type="fwidth">ancho completo</type>
			<type key="d0" type="hwidth">ancho medio</type>
			<type key="d0" type="npinyin">Numérico</type>
			<type key="hc" type="h11">sistema de 12 horas (0–11)</type>
			<type key="hc" type="h12">sistema de 12 horas (1–12)</type>
			<type key="hc" type="h23">sistema de 24 horas (0–23)</type>
			<type key="hc" type="h24">sistema de 24 horas (1–24)</type>
			<type key="lb" type="loose">estilo de quebra de liña flexible</type>
			<type key="lb" type="normal">estilo de quebra de liña normal</type>
			<type key="lb" type="strict">estilo de quebra de liña estrita</type>
			<type key="m0" type="bgn">transliteración do BGN</type>
			<type key="m0" type="ungegn">transliteración do UNGEGN</type>
			<type key="ms" type="metric">sistema métrico decimal</type>
			<type key="ms" type="uksystem">sistema imperial de unidades</type>
			<type key="ms" type="ussystem">sistema estadounidense de unidades</type>
			<type key="numbers" type="arab">díxitos indoarábigos</type>
			<type key="numbers" type="arabext">díxitos indoarábigos ampliados</type>
			<type key="numbers" type="armn">numeración armenia</type>
			<type key="numbers" type="armnlow">numeración armenia en minúscula</type>
			<type key="numbers" type="beng">díxitos bengalís</type>
			<type key="numbers" type="deva">díxitos devanagáricos</type>
			<type key="numbers" type="ethi">numeración etíope</type>
			<type key="numbers" type="finance">Números financeiros</type>
			<type key="numbers" type="fullwide">díxitos de ancho completo</type>
			<type key="numbers" type="geor">numeración xeorxiana</type>
			<type key="numbers" type="grek">numeración grega</type>
			<type key="numbers" type="greklow">numeración grega en minúscula</type>
			<type key="numbers" type="gujr">díxitos guxaratis</type>
			<type key="numbers" type="guru">díxitos do gurmukhi</type>
			<type key="numbers" type="hanidec">numeración decimal chinesa</type>
			<type key="numbers" type="hans">numeración chinesa simplificada</type>
			<type key="numbers" type="hansfin">numeración financeira chinesa simplificada</type>
			<type key="numbers" type="hant">numeración chinesa tradicional</type>
			<type key="numbers" type="hantfin">numeración financeira chinesa tradicional</type>
			<type key="numbers" type="hebr">numeración hebrea</type>
			<type key="numbers" type="jpan">numeración xaponesa</type>
			<type key="numbers" type="jpanfin">numeración financeira xaponesa</type>
			<type key="numbers" type="khmr">díxitos khmer</type>
			<type key="numbers" type="knda">díxitos kannarás</type>
			<type key="numbers" type="laoo">díxitos laosianos</type>
			<type key="numbers" type="latn">díxitos occidentais</type>
			<type key="numbers" type="mlym">díxitos malabares</type>
			<type key="numbers" type="mong">Díxitos mongoles</type>
			<type key="numbers" type="mymr">díxitos birmanos</type>
			<type key="numbers" type="native">Díxitos orixinais</type>
			<type key="numbers" type="orya">díxitos do odiá</type>
			<type key="numbers" type="roman">numeración romana</type>
			<type key="numbers" type="romanlow">numeración romana en minúsculas</type>
			<type key="numbers" type="taml">numeración támil tradicional</type>
			<type key="numbers" type="tamldec">díxitos do támil</type>
			<type key="numbers" type="telu">díxitos de telugu</type>
			<type key="numbers" type="thai">díxitos tailandeses</type>
			<type key="numbers" type="tibt">díxitos tibetanos</type>
			<type key="numbers" type="traditional">Numeros tradicionais</type>
			<type key="numbers" type="vaii">Díxitos Vai</type>
		</types>
		<measurementSystemNames>
			<measurementSystemName type="metric">métrico decimal</measurementSystemName>
			<measurementSystemName type="UK">británico</measurementSystemName>
			<measurementSystemName type="US">estadounidense</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Idioma: {0}</codePattern>
			<codePattern type="script">Sistema de escritura: {0}</codePattern>
			<codePattern type="territory">Rexión: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a á b c d e é f g h i í ï j k l m n ñ o ó p q r s t u ú ü v w x y z]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[ª à ă â å ä ã ā æ ɑ ç è ĕ ê ë ē ì ĭ î ī º ò ŏ ô ö õ ø ō œ ù ŭ û ū]</exemplarCharacters>
		<exemplarCharacters type="index">[A B C D E F G H I J K L M N Ñ O P Q R S T U V W X Y Z]</exemplarCharacters>
		<exemplarCharacters type="numbers">[\- ‑ , . % ‰ + 0 1 2 3 4 5 6 7 8 9]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[\- ‐ ‑ – — , ; \: ! ¡ ? ¿ . … ' ‘ ’ &quot; “ ” « » ( ) \[ \] § @ * / \\ \&amp; # † ‡ ′ ″]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<ellipsis type="word-final">{0}…</ellipsis>
		<ellipsis type="word-initial">…{0}</ellipsis>
		<ellipsis type="word-medial">{0}… {1}</ellipsis>
		<moreInformation>?</moreInformation>
		<parseLenients scope="date" level="lenient">
			<parseLenient sample="-" draft="contributed">[\--/]</parseLenient>
			<parseLenient sample=":" draft="contributed">[\:∶]</parseLenient>
		</parseLenients>
		<parseLenients scope="general" level="lenient">
			<parseLenient sample="." draft="contributed">[.․。︒﹒．｡]</parseLenient>
			<parseLenient sample="’" draft="contributed">['ʼ՚᾽᾿’＇]</parseLenient>
			<parseLenient sample="%" draft="contributed">[%٪﹪％]</parseLenient>
			<parseLenient sample="‰" draft="contributed">[؉‰]</parseLenient>
			<parseLenient sample="$" draft="contributed">[\$﹩＄$]</parseLenient>
			<parseLenient sample="£" draft="contributed">[£₤]</parseLenient>
			<parseLenient sample="¥" draft="contributed">[¥￥]</parseLenient>
			<parseLenient sample="₩" draft="contributed">[₩￦]</parseLenient>
			<parseLenient sample="₹" draft="contributed">[₨₹{Rp}{Rs}]</parseLenient>
		</parseLenients>
		<parseLenients scope="number" level="lenient">
			<parseLenient sample="-" draft="contributed">[\-‒⁻₋−➖﹣－]</parseLenient>
			<parseLenient sample="," draft="contributed">[,،٫、︐︑﹐﹑，､]</parseLenient>
			<parseLenient sample="+" draft="contributed">[+⁺₊➕﬩﹢＋]</parseLenient>
		</parseLenients>
		<parseLenients scope="number" level="stricter">
			<parseLenient sample="," draft="contributed">[,٫︐﹐，]</parseLenient>
			<parseLenient sample="." draft="contributed">[.․﹒．｡]</parseLenient>
		</parseLenients>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, d 'de' MMMM 'de' Y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d 'de' MMMM 'de' y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d 'de' MMM 'de' y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'ás' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'ás' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="Bh">h B</dateFormatItem>
						<dateFormatItem id="Bhm">h:mm B</dateFormatItem>
						<dateFormatItem id="Bhms">h:mm:ss B</dateFormatItem>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="E">ccc</dateFormatItem>
						<dateFormatItem id="EBhm">E, h:mm B</dateFormatItem>
						<dateFormatItem id="EBhms">E, h:mm:ss B</dateFormatItem>
						<dateFormatItem id="Ed">E d</dateFormatItem>
						<dateFormatItem id="Ehm">E, h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E, HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E, h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E, HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">dd/MM</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMdd" draft="contributed">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
						<dateFormatItem id="yM">M-y</dateFormatItem>
						<dateFormatItem id="yMd">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMM" draft="contributed">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMd">d 'de' MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d 'de' MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ 'de' y</dateFormatItem>
						<dateFormatItem id="yyyy">y G</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMd">d/M/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, d/M/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMM">MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQ y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ 'de' y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="Bh">
							<greatestDifference id="B">h B – h B</greatestDifference>
							<greatestDifference id="h">h–h B</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Bhm">
							<greatestDifference id="B">h:mm B – h:mm B</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm B</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm B</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Gy">
							<greatestDifference id="G">y G – y G</greatestDifference>
							<greatestDifference id="y">y–y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyM">
							<greatestDifference id="G">MM/y GGGGG – MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">MM/y – MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">MM/y – MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMd">
							<greatestDifference id="d">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="G">dd/MM/y GGGGG – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMEd">
							<greatestDifference id="d">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="G">E, dd/MM/y GGGGG – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMM">
							<greatestDifference id="G">MMM 'de' y G – MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">MMM–MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMM 'de' y – MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMMd">
							<greatestDifference id="d">d–d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="G">d 'de' MMM 'de' y G – d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y – d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMMEd">
							<greatestDifference id="d">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="G">E, d 'de' MMM 'de' y G – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y – E, d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d 'de' MMM</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d 'de' MMM – E, d 'de' MMM</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y GGGGG</greatestDifference>
							<greatestDifference id="y">MM/y – MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y GGGGG</greatestDifference>
							<greatestDifference id="M">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y GGGGG</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMM 'de' y – MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y – d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y – E, d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMMM 'de' y – MMMM 'de' y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">xan.</month>
							<month type="2">feb.</month>
							<month type="3">mar.</month>
							<month type="4">abr.</month>
							<month type="5">maio</month>
							<month type="6">xuño</month>
							<month type="7">xul.</month>
							<month type="8">ago.</month>
							<month type="9">set.</month>
							<month type="10">out.</month>
							<month type="11">nov.</month>
							<month type="12">dec.</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">x.</month>
							<month type="2">f.</month>
							<month type="3">m.</month>
							<month type="4">a.</month>
							<month type="5">m.</month>
							<month type="6">x.</month>
							<month type="7">x.</month>
							<month type="8">a.</month>
							<month type="9">s.</month>
							<month type="10">o.</month>
							<month type="11">n.</month>
							<month type="12">d.</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">xaneiro</month>
							<month type="2">febreiro</month>
							<month type="3">marzo</month>
							<month type="4">abril</month>
							<month type="5">maio</month>
							<month type="6">xuño</month>
							<month type="7">xullo</month>
							<month type="8">agosto</month>
							<month type="9">setembro</month>
							<month type="10">outubro</month>
							<month type="11">novembro</month>
							<month type="12">decembro</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Xan.</month>
							<month type="2">Feb.</month>
							<month type="3">Mar.</month>
							<month type="4">Abr.</month>
							<month type="5">Maio</month>
							<month type="6">Xuño</month>
							<month type="7">Xul.</month>
							<month type="8">Ago.</month>
							<month type="9">Set.</month>
							<month type="10">Out.</month>
							<month type="11">Nov.</month>
							<month type="12">Dec.</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">X</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">X</month>
							<month type="7">X</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Xaneiro</month>
							<month type="2">Febreiro</month>
							<month type="3">Marzo</month>
							<month type="4">Abril</month>
							<month type="5">Maio</month>
							<month type="6">Xuño</month>
							<month type="7">Xullo</month>
							<month type="8">Agosto</month>
							<month type="9">Setembro</month>
							<month type="10">Outubro</month>
							<month type="11">Novembro</month>
							<month type="12">Decembro</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">dom.</day>
							<day type="mon">luns</day>
							<day type="tue">mar.</day>
							<day type="wed">mér.</day>
							<day type="thu">xov.</day>
							<day type="fri">ven.</day>
							<day type="sat">sáb.</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">d.</day>
							<day type="mon">l.</day>
							<day type="tue">m.</day>
							<day type="wed">m.</day>
							<day type="thu">x.</day>
							<day type="fri">v.</day>
							<day type="sat">s.</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">do.</day>
							<day type="mon">lu.</day>
							<day type="tue">ma.</day>
							<day type="wed">mé.</day>
							<day type="thu">xo.</day>
							<day type="fri">ve.</day>
							<day type="sat">sá.</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">domingo</day>
							<day type="mon">luns</day>
							<day type="tue">martes</day>
							<day type="wed">mércores</day>
							<day type="thu">xoves</day>
							<day type="fri">venres</day>
							<day type="sat">sábado</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">Dom.</day>
							<day type="mon">Luns</day>
							<day type="tue">Mar.</day>
							<day type="wed">Mér.</day>
							<day type="thu">Xov.</day>
							<day type="fri">Ven.</day>
							<day type="sat">Sáb.</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">D</day>
							<day type="mon">L</day>
							<day type="tue">M</day>
							<day type="wed">M</day>
							<day type="thu">X</day>
							<day type="fri">V</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">Do</day>
							<day type="mon">Lu</day>
							<day type="tue">Ma</day>
							<day type="wed">Mé</day>
							<day type="thu">Xo</day>
							<day type="fri">Ve</day>
							<day type="sat">Sá</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Domingo</day>
							<day type="mon">Luns</day>
							<day type="tue">Martes</day>
							<day type="wed">Mércores</day>
							<day type="thu">Xoves</day>
							<day type="fri">Venres</day>
							<day type="sat">Sábado</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1.º trimestre</quarter>
							<quarter type="2">2.º trimestre</quarter>
							<quarter type="3">3.º trimestre</quarter>
							<quarter type="4">4.º trimestre</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1.º trimestre</quarter>
							<quarter type="2">2.º trimestre</quarter>
							<quarter type="3">3.º trimestre</quarter>
							<quarter type="4">4.º trimestre</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="midnight">da noite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">da madrugada</dayPeriod>
							<dayPeriod type="morning2">da mañá</dayPeriod>
							<dayPeriod type="afternoon1">do mediodía</dayPeriod>
							<dayPeriod type="evening1">da tarde</dayPeriod>
							<dayPeriod type="night1">da noite</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="narrow">
							<dayPeriod type="midnight">da noite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">da madrugada</dayPeriod>
							<dayPeriod type="morning2">da mañá</dayPeriod>
							<dayPeriod type="afternoon1">do mediodía</dayPeriod>
							<dayPeriod type="evening1">da tarde</dayPeriod>
							<dayPeriod type="night1">da noite</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="midnight">da noite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">da madrugada</dayPeriod>
							<dayPeriod type="morning2">da mañá</dayPeriod>
							<dayPeriod type="afternoon1">do mediodía</dayPeriod>
							<dayPeriod type="evening1">da tarde</dayPeriod>
							<dayPeriod type="night1">da noite</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
					<dayPeriodContext type="stand-alone">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="midnight">medianoite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">madrugada</dayPeriod>
							<dayPeriod type="morning2">mañá</dayPeriod>
							<dayPeriod type="afternoon1">mediodía</dayPeriod>
							<dayPeriod type="evening1">tarde</dayPeriod>
							<dayPeriod type="night1">noite</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="narrow">
							<dayPeriod type="midnight">medianoite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">madrugada</dayPeriod>
							<dayPeriod type="morning2">mañá</dayPeriod>
							<dayPeriod type="afternoon1">mediodía</dayPeriod>
							<dayPeriod type="evening1">tarde</dayPeriod>
							<dayPeriod type="night1">noite</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="midnight">medianoite</dayPeriod>
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
							<dayPeriod type="morning1">madrugada</dayPeriod>
							<dayPeriod type="morning2">mañá</dayPeriod>
							<dayPeriod type="afternoon1">mediodía</dayPeriod>
							<dayPeriod type="evening1">tarde</dayPeriod>
							<dayPeriod type="night1">noite</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0">antes de Cristo</era>
						<era type="0" alt="variant">antes da era común</era>
						<era type="1">despois de Cristo</era>
						<era type="1" alt="variant">da era común</era>
					</eraNames>
					<eraAbbr>
						<era type="0">a.C.</era>
						<era type="0" alt="variant">a.e.c.</era>
						<era type="1">d.C.</era>
						<era type="1" alt="variant">e.c.</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, d 'de' MMMM 'de' y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d 'de' MMMM 'de' y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d 'de' MMM 'de' y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{0} 'do' {1}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{0} 'do' {1}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{0}, {1}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{0}, {1}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="Bh">h B</dateFormatItem>
						<dateFormatItem id="Bhm">h:mm B</dateFormatItem>
						<dateFormatItem id="Bhms">h:mm:ss B</dateFormatItem>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="E">ccc</dateFormatItem>
						<dateFormatItem id="EBhm">E, h:mm B</dateFormatItem>
						<dateFormatItem id="EBhms">E, h:mm:ss B</dateFormatItem>
						<dateFormatItem id="Ed">E d</dateFormatItem>
						<dateFormatItem id="Ehm">E, h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E, HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E, h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E, HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="hmsv">h:mm:ss a v</dateFormatItem>
						<dateFormatItem id="Hmsv">HH:mm:ss v</dateFormatItem>
						<dateFormatItem id="hmv">h:mm a v</dateFormatItem>
						<dateFormatItem id="Hmv">HH:mm v</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, d/M</dateFormatItem>
						<dateFormatItem id="MMdd">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMW" count="one">W.'ª' 'semana' 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMW" count="other">W.'ª' 'semana' 'de' MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMd">d/M/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, d/M/y</dateFormatItem>
						<dateFormatItem id="yMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMd">d 'de' MMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d 'de' MMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ 'de' y</dateFormatItem>
						<dateFormatItem id="yw" count="one">w.'ª' 'semana' 'de' Y</dateFormatItem>
						<dateFormatItem id="yw" count="other">w.'ª' 'semana' 'de' Y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Timezone">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="Bh">
							<greatestDifference id="B">h B – h B</greatestDifference>
							<greatestDifference id="h">h–h B</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Bhm">
							<greatestDifference id="B">h:mm B – h:mm B</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm B</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm B</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Gy">
							<greatestDifference id="G">y G – y G</greatestDifference>
							<greatestDifference id="y">y–y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyM">
							<greatestDifference id="G">MM/y GGGGG – MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">MM/y – MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">MM/y – MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMd">
							<greatestDifference id="d">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="G">dd/MM/y GGGGG – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">dd/MM/y – dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMEd">
							<greatestDifference id="d">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="G">E, dd/MM/y GGGGG – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
							<greatestDifference id="y">E, dd/MM/y – E, dd/MM/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMM">
							<greatestDifference id="G">MMM 'de' y G – MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">MMM–MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMM 'de' y – MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMMd">
							<greatestDifference id="d">d–d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="G">d 'de' MMM 'de' y G – d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y – d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="GyMMMEd">
							<greatestDifference id="d">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="G">E, d 'de' MMM 'de' y G – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y – E, d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M–M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">d/M – d/M</greatestDifference>
							<greatestDifference id="M">d/M – d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, d/M – E, d/M</greatestDifference>
							<greatestDifference id="M">E, d/M – E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM–MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d–d 'de' MMM</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d 'de' MMM – E, d 'de' MMM</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="M">d/M/y – d/M/y</greatestDifference>
							<greatestDifference id="y">dd/MM/y – dd/MM/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, d/M/y – E, d/M/y</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y – E, dd/MM/y</greatestDifference>
							<greatestDifference id="y">E, d/M/y – E, d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM–MMM 'de' y</greatestDifference>
							<greatestDifference id="y">MMM 'de' y – MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d–d 'de' MMMM 'de' y</greatestDifference>
							<greatestDifference id="M">d 'de' MMM – d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y – d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d MMM – E, d MMM y</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM – E, d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y – E, d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM–MMMM 'de' y</greatestDifference>
							<greatestDifference id="y">MMMM 'de' y – MMMM 'de' y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>era</displayName>
			</field>
			<field type="era-short">
				<displayName>era</displayName>
			</field>
			<field type="era-narrow">
				<displayName>era</displayName>
			</field>
			<field type="year">
				<displayName>ano</displayName>
				<relative type="-1">o ano pasado</relative>
				<relative type="0">este ano</relative>
				<relative type="1">o próximo ano</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">en {0} anos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} anos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="year-short">
				<displayName>ano</displayName>
				<relative type="-1">o ano pasado</relative>
				<relative type="0">este ano</relative>
				<relative type="1">o próximo ano</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">en {0} anos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} anos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="year-narrow">
				<displayName>ano</displayName>
				<relative type="-1">o ano pas.</relative>
				<relative type="0">este ano</relative>
				<relative type="1">o próx. ano</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">en {0} anos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} anos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="quarter">
				<displayName>trimestre</displayName>
				<relative type="-1">o trimestre pasado</relative>
				<relative type="0">este trimestre</relative>
				<relative type="1">o próximo trimestre</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} trimestre</relativeTimePattern>
					<relativeTimePattern count="other">en {0} trimestres</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} trimestre</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} trimestres</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="quarter-short">
				<displayName>trim.</displayName>
				<relative type="-1">trim. pasado</relative>
				<relative type="0">este trim.</relative>
				<relative type="1">trim. seguinte</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} trim.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} trim.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} trim.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} trim.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="quarter-narrow">
				<displayName>trim.</displayName>
				<relative type="-1">trim. pasado</relative>
				<relative type="0">este trim.</relative>
				<relative type="1">trim. seguinte</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} trim.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} trim.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} trim.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} trim.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>mes</displayName>
				<relative type="-1">o mes pasado</relative>
				<relative type="0">este mes</relative>
				<relative type="1">o próximo mes</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">en {0} meses</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} meses</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month-short">
				<displayName>mes</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">en {0} meses</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} meses</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month-narrow">
				<displayName>mes</displayName>
				<relative type="-1">o mes pas.</relative>
				<relative type="0">este mes</relative>
				<relative type="1">o próx. mes</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">en {0} meses</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mes</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} meses</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>semana</displayName>
				<relative type="-1">a semana pasada</relative>
				<relative type="0">esta semana</relative>
				<relative type="1">a próxima semana</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">en {0} semanas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} semanas</relativeTimePattern>
				</relativeTime>
				<relativePeriod>a semana do {0}</relativePeriod>
			</field>
			<field type="week-short">
				<displayName>sem.</displayName>
				<relative type="-1">a sem. pasada</relative>
				<relative type="0">esta sem.</relative>
				<relative type="1">a próxima sem.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} sem.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} sem.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} sem.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} sem.</relativeTimePattern>
				</relativeTime>
				<relativePeriod>a semana do {0}</relativePeriod>
			</field>
			<field type="week-narrow">
				<displayName>sem.</displayName>
				<relative type="-1">a sem. pas.</relative>
				<relative type="0">esta sem.</relative>
				<relative type="1">a próx. sem.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} sem.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} sem.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} sem.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} sem.</relativeTimePattern>
				</relativeTime>
				<relativePeriod>a semana do {0}</relativePeriod>
			</field>
			<field type="weekOfMonth">
				<displayName>semana do mes</displayName>
			</field>
			<field type="weekOfMonth-short">
				<displayName>sem. do mes</displayName>
			</field>
			<field type="weekOfMonth-narrow">
				<displayName>sem. do mes</displayName>
			</field>
			<field type="day">
				<displayName>día</displayName>
				<relative type="-2">antonte</relative>
				<relative type="-1">onte</relative>
				<relative type="0">hoxe</relative>
				<relative type="1">mañá</relative>
				<relative type="2">pasadomañá</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} día</relativeTimePattern>
					<relativeTimePattern count="other">en {0} días</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} día</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} días</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day-short">
				<displayName>día</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} día</relativeTimePattern>
					<relativeTimePattern count="other">en {0} días</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} día</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} días</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day-narrow">
				<displayName>día</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} día</relativeTimePattern>
					<relativeTimePattern count="other">en {0} días</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} día</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} días</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="dayOfYear">
				<displayName>día do ano</displayName>
			</field>
			<field type="dayOfYear-short">
				<displayName>día do ano</displayName>
			</field>
			<field type="dayOfYear-narrow">
				<displayName>día do ano</displayName>
			</field>
			<field type="weekday">
				<displayName>día da semana</displayName>
			</field>
			<field type="weekday-short">
				<displayName>día da sem.</displayName>
			</field>
			<field type="weekday-narrow">
				<displayName>día da sem.</displayName>
			</field>
			<field type="weekdayOfMonth">
				<displayName>semana do mes</displayName>
			</field>
			<field type="weekdayOfMonth-short">
				<displayName>sem. do mes</displayName>
			</field>
			<field type="weekdayOfMonth-narrow">
				<displayName>sem. do mes</displayName>
			</field>
			<field type="sun">
				<relative type="-1">o domingo pasado</relative>
				<relative type="0">este domingo</relative>
				<relative type="1">o próximo domingo</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} domingo</relativeTimePattern>
					<relativeTimePattern count="other">en {0} domingos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} domingo</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} domingos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="sun-short">
				<relative type="-1">o dom. pasado</relative>
				<relative type="0">este dom.</relative>
				<relative type="1">o próximo dom.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} dom.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} dom.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} dom.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} dom.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="sun-narrow">
				<relative type="-1">o dom. pas.</relative>
				<relative type="0">este dom.</relative>
				<relative type="1">o próx. dom.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} dom.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} dom.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} dom.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} dom.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="mon">
				<relative type="-1">o luns pasado</relative>
				<relative type="0">este luns</relative>
				<relative type="1">o próximo luns</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">en {0} luns</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} luns</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="mon-short">
				<relative type="-1">o luns pas.</relative>
				<relative type="0">este luns</relative>
				<relative type="1">o próx. luns</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">en {0} luns</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} luns</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="mon-narrow">
				<relative type="-1">o luns pas.</relative>
				<relative type="0">este luns</relative>
				<relative type="1">o próx. luns</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">en {0} luns</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} luns</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} luns</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="tue">
				<relative type="-1">o martes pasado</relative>
				<relative type="0">este martes</relative>
				<relative type="1">o próximo martes</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} martes</relativeTimePattern>
					<relativeTimePattern count="other">en {0} martes</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} martes</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} martes</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="tue-short">
				<relative type="-1">o mar. pasado</relative>
				<relative type="0">este mar.</relative>
				<relative type="1">o próximo mar.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mar.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} mar.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mar.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} mar.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="tue-narrow">
				<relative type="-1">o mar. pas.</relative>
				<relative type="0">este mar.</relative>
				<relative type="1">o próx. mar.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mar.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} mar.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mar.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} mar.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="wed">
				<relative type="-1">o mércores pasado</relative>
				<relative type="0">este mércores</relative>
				<relative type="1">o próximo mércores</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mércores</relativeTimePattern>
					<relativeTimePattern count="other">en {0} mércores</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mércores</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} mércores</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="wed-short">
				<relative type="-1">o mér. pasado</relative>
				<relative type="0">este mér.</relative>
				<relative type="1">o próximo mér.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mér.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} mér.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mér.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} mér.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="wed-narrow">
				<relative type="-1">o mér. pas.</relative>
				<relative type="0">este mér.</relative>
				<relative type="1">o próx. mér.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} mér.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} mér.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} mér.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} mér.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="thu">
				<relative type="-1">o xoves pasado</relative>
				<relative type="0">este xoves</relative>
				<relative type="1">o próximo xoves</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} xoves</relativeTimePattern>
					<relativeTimePattern count="other">en {0} xoves</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} xoves</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} xoves</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="thu-short">
				<relative type="-1">o xov. pasado</relative>
				<relative type="0">este xov.</relative>
				<relative type="1">o próximo xov.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} xov.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} xov.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} xov.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} xov.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="thu-narrow">
				<relative type="-1">o xov. pas.</relative>
				<relative type="0">este xov.</relative>
				<relative type="1">o próx. xov.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} xov.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} xov.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} xov.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} xov.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="fri">
				<relative type="-1">o venres pasado</relative>
				<relative type="0">este venres</relative>
				<relative type="1">o próximo venres</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} venres</relativeTimePattern>
					<relativeTimePattern count="other">en {0} venres</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} venres</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} venres</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="fri-short">
				<relative type="-1">o ven. pasado</relative>
				<relative type="0">este ven.</relative>
				<relative type="1">o próximo ven.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} ven.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} ven.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} ven.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} ven.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="fri-narrow">
				<relative type="-1">o ven. pas.</relative>
				<relative type="0">este ven.</relative>
				<relative type="1">o próx. ven.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} ven.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} ven.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} ven.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} ven.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="sat">
				<relative type="-1">o sábado pasado</relative>
				<relative type="0">este sábado</relative>
				<relative type="1">o próximo sábado</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} sábado</relativeTimePattern>
					<relativeTimePattern count="other">en {0} sábados</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} sábado</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} sábados</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="sat-short">
				<relative type="-1">o sáb. pasado</relative>
				<relative type="0">este sáb.</relative>
				<relative type="1">o próximo sáb.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} sáb.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} sáb.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} sáb.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} sáb.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="sat-narrow">
				<relative type="-1">o sáb. pas.</relative>
				<relative type="0">este sáb.</relative>
				<relative type="1">o próx. sáb.</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} sáb.</relativeTimePattern>
					<relativeTimePattern count="other">en {0} sáb.</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} sáb.</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} sáb.</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="dayperiod-short">
				<displayName>a.m./p.m.</displayName>
			</field>
			<field type="dayperiod">
				<displayName>a.m./p.m.</displayName>
			</field>
			<field type="dayperiod-narrow">
				<displayName>a.m./p.m.</displayName>
			</field>
			<field type="hour">
				<displayName>hora</displayName>
				<relative type="0">esta hora</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">en {0} horas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} horas</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="hour-short">
				<displayName>h</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} h</relativeTimePattern>
					<relativeTimePattern count="other">en {0} h</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} h</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} h</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="hour-narrow">
				<displayName>h</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} h</relativeTimePattern>
					<relativeTimePattern count="other">en {0} h</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} h</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} h</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>minuto</displayName>
				<relative type="0">este minuto</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">en {0} minutos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} minutos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute-short">
				<displayName>min</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} min</relativeTimePattern>
					<relativeTimePattern count="other">en {0} min</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} min</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} min</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute-narrow">
				<displayName>min</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} min</relativeTimePattern>
					<relativeTimePattern count="other">en {0} min</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} min</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} min</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>segundo</displayName>
				<relative type="0">agora</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">en {0} segundos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} segundos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second-short">
				<displayName>s</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} s</relativeTimePattern>
					<relativeTimePattern count="other">en {0} s</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} s</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} s</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second-narrow">
				<displayName>s</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">en {0} s</relativeTimePattern>
					<relativeTimePattern count="other">en {0} s</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">hai {0} s</relativeTimePattern>
					<relativeTimePattern count="other">hai {0} s</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>fuso horario</displayName>
			</field>
			<field type="zone-short">
				<displayName>fuso</displayName>
			</field>
			<field type="zone-narrow">
				<displayName>fuso</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>Horario de: {0}</regionFormat>
			<regionFormat type="daylight">Horario de verán de: {0}</regionFormat>
			<regionFormat type="standard">Horario estándar de: {0}</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="America/Santa_Isabel">
				<exemplarCity>Santa Isabel</exemplarCity>
			</zone>
			<zone type="Etc/UTC">
				<long>
					<standard>Horario universal coordinado</standard>
				</long>
			</zone>
			<zone type="Etc/Unknown">
				<exemplarCity>Cidade descoñecida</exemplarCity>
			</zone>
			<zone type="Europe/Andorra">
				<exemplarCity>Andorra</exemplarCity>
			</zone>
			<zone type="Asia/Dubai">
				<exemplarCity>Dubai</exemplarCity>
			</zone>
			<zone type="Asia/Kabul">
				<exemplarCity>Cabul</exemplarCity>
			</zone>
			<zone type="America/Anguilla">
				<exemplarCity>Anguila</exemplarCity>
			</zone>
			<zone type="Europe/Tirane">
				<exemplarCity>Tirana</exemplarCity>
			</zone>
			<zone type="Asia/Yerevan">
				<exemplarCity>Iereván</exemplarCity>
			</zone>
			<zone type="Africa/Luanda">
				<exemplarCity>Luanda</exemplarCity>
			</zone>
			<zone type="Antarctica/Rothera">
				<exemplarCity>Rothera</exemplarCity>
			</zone>
			<zone type="Antarctica/Palmer">
				<exemplarCity>Palmer</exemplarCity>
			</zone>
			<zone type="Antarctica/Troll">
				<exemplarCity>Troll</exemplarCity>
			</zone>
			<zone type="Antarctica/Syowa">
				<exemplarCity>Showa</exemplarCity>
			</zone>
			<zone type="Antarctica/Mawson">
				<exemplarCity>Mawson</exemplarCity>
			</zone>
			<zone type="Antarctica/Davis">
				<exemplarCity>Davis</exemplarCity>
			</zone>
			<zone type="Antarctica/Vostok">
				<exemplarCity>Vostok</exemplarCity>
			</zone>
			<zone type="Antarctica/Casey">
				<exemplarCity>Casey</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont-d’Urville</exemplarCity>
			</zone>
			<zone type="Antarctica/McMurdo">
				<exemplarCity>McMurdo</exemplarCity>
			</zone>
			<zone type="America/Argentina/Rio_Gallegos">
				<exemplarCity>Río Gallegos</exemplarCity>
			</zone>
			<zone type="America/Mendoza">
				<exemplarCity>Mendoza</exemplarCity>
			</zone>
			<zone type="America/Argentina/San_Juan">
				<exemplarCity>San Juan</exemplarCity>
			</zone>
			<zone type="America/Argentina/Ushuaia">
				<exemplarCity>Ushuaia</exemplarCity>
			</zone>
			<zone type="America/Argentina/La_Rioja">
				<exemplarCity>A Rioxa</exemplarCity>
			</zone>
			<zone type="America/Argentina/San_Luis">
				<exemplarCity>San Luis</exemplarCity>
			</zone>
			<zone type="America/Catamarca">
				<exemplarCity>Catamarca</exemplarCity>
			</zone>
			<zone type="America/Argentina/Salta">
				<exemplarCity>Salta</exemplarCity>
			</zone>
			<zone type="America/Jujuy">
				<exemplarCity>Jujuy</exemplarCity>
			</zone>
			<zone type="America/Argentina/Tucuman">
				<exemplarCity>Tucumán</exemplarCity>
			</zone>
			<zone type="America/Cordoba">
				<exemplarCity>Córdoba</exemplarCity>
			</zone>
			<zone type="America/Buenos_Aires">
				<exemplarCity>Buenos Aires</exemplarCity>
			</zone>
			<zone type="Pacific/Pago_Pago">
				<exemplarCity>Pago Pago</exemplarCity>
			</zone>
			<zone type="Europe/Vienna">
				<exemplarCity>Viena</exemplarCity>
			</zone>
			<zone type="Australia/Perth">
				<exemplarCity>Perth</exemplarCity>
			</zone>
			<zone type="Australia/Eucla">
				<exemplarCity>Eucla</exemplarCity>
			</zone>
			<zone type="Australia/Darwin">
				<exemplarCity>Darwin</exemplarCity>
			</zone>
			<zone type="Australia/Adelaide">
				<exemplarCity>Adelaida</exemplarCity>
			</zone>
			<zone type="Australia/Broken_Hill">
				<exemplarCity>Broken Hill</exemplarCity>
			</zone>
			<zone type="Australia/Melbourne">
				<exemplarCity>Melbourne</exemplarCity>
			</zone>
			<zone type="Australia/Currie">
				<exemplarCity>Currie</exemplarCity>
			</zone>
			<zone type="Australia/Hobart">
				<exemplarCity>Hobart</exemplarCity>
			</zone>
			<zone type="Australia/Lindeman">
				<exemplarCity>Lindeman</exemplarCity>
			</zone>
			<zone type="Australia/Sydney">
				<exemplarCity>Sidney</exemplarCity>
			</zone>
			<zone type="Australia/Brisbane">
				<exemplarCity>Brisbane</exemplarCity>
			</zone>
			<zone type="Antarctica/Macquarie">
				<exemplarCity>Macquarie</exemplarCity>
			</zone>
			<zone type="Australia/Lord_Howe">
				<exemplarCity>Lord Howe</exemplarCity>
			</zone>
			<zone type="America/Aruba">
				<exemplarCity>Aruba</exemplarCity>
			</zone>
			<zone type="Europe/Mariehamn">
				<exemplarCity>Mariehamn</exemplarCity>
			</zone>
			<zone type="Asia/Baku">
				<exemplarCity>Bacú</exemplarCity>
			</zone>
			<zone type="Europe/Sarajevo">
				<exemplarCity>Saraievo</exemplarCity>
			</zone>
			<zone type="America/Barbados">
				<exemplarCity>Barbados</exemplarCity>
			</zone>
			<zone type="Asia/Dhaka">
				<exemplarCity>Dhaka</exemplarCity>
			</zone>
			<zone type="Europe/Brussels">
				<exemplarCity>Bruxelas</exemplarCity>
			</zone>
			<zone type="Africa/Ouagadougou">
				<exemplarCity>Uagadugu</exemplarCity>
			</zone>
			<zone type="Europe/Sofia">
				<exemplarCity>Sofía</exemplarCity>
			</zone>
			<zone type="Asia/Bahrain">
				<exemplarCity>Bahrain</exemplarCity>
			</zone>
			<zone type="Africa/Bujumbura">
				<exemplarCity>Bujumbura</exemplarCity>
			</zone>
			<zone type="Africa/Porto-Novo">
				<exemplarCity>Porto-Novo</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>Saint Barthélemy</exemplarCity>
			</zone>
			<zone type="Atlantic/Bermuda">
				<exemplarCity>Illas Bermudas</exemplarCity>
			</zone>
			<zone type="Asia/Brunei">
				<exemplarCity>Brunei</exemplarCity>
			</zone>
			<zone type="America/La_Paz">
				<exemplarCity>A Paz</exemplarCity>
			</zone>
			<zone type="America/Kralendijk">
				<exemplarCity>Kralendijk</exemplarCity>
			</zone>
			<zone type="America/Eirunepe">
				<exemplarCity>Eirunepé</exemplarCity>
			</zone>
			<zone type="America/Rio_Branco">
				<exemplarCity>Río Branco</exemplarCity>
			</zone>
			<zone type="America/Porto_Velho">
				<exemplarCity>Porto Velho</exemplarCity>
			</zone>
			<zone type="America/Boa_Vista">
				<exemplarCity>Boa Vista</exemplarCity>
			</zone>
			<zone type="America/Manaus">
				<exemplarCity>Manaus</exemplarCity>
			</zone>
			<zone type="America/Cuiaba">
				<exemplarCity>Cuiabá</exemplarCity>
			</zone>
			<zone type="America/Santarem">
				<exemplarCity>Santarém</exemplarCity>
			</zone>
			<zone type="America/Campo_Grande">
				<exemplarCity>Campo Grande</exemplarCity>
			</zone>
			<zone type="America/Belem">
				<exemplarCity>Belém</exemplarCity>
			</zone>
			<zone type="America/Araguaina">
				<exemplarCity>Araguaína</exemplarCity>
			</zone>
			<zone type="America/Sao_Paulo">
				<exemplarCity>São Paulo</exemplarCity>
			</zone>
			<zone type="America/Bahia">
				<exemplarCity>Baía</exemplarCity>
			</zone>
			<zone type="America/Fortaleza">
				<exemplarCity>Fortaleza</exemplarCity>
			</zone>
			<zone type="America/Maceio">
				<exemplarCity>Maceió</exemplarCity>
			</zone>
			<zone type="America/Recife">
				<exemplarCity>Recife</exemplarCity>
			</zone>
			<zone type="America/Noronha">
				<exemplarCity>Noronha</exemplarCity>
			</zone>
			<zone type="America/Nassau">
				<exemplarCity>Nassau</exemplarCity>
			</zone>
			<zone type="Africa/Gaborone">
				<exemplarCity>Gaborone</exemplarCity>
			</zone>
			<zone type="Europe/Minsk">
				<exemplarCity>Minsk</exemplarCity>
			</zone>
			<zone type="America/Belize">
				<exemplarCity>Belize</exemplarCity>
			</zone>
			<zone type="America/Dawson">
				<exemplarCity>Dawson</exemplarCity>
			</zone>
			<zone type="America/Whitehorse">
				<exemplarCity>Whitehorse</exemplarCity>
			</zone>
			<zone type="America/Inuvik">
				<exemplarCity>Inuvik</exemplarCity>
			</zone>
			<zone type="America/Vancouver">
				<exemplarCity>Vancouver</exemplarCity>
			</zone>
			<zone type="America/Fort_Nelson">
				<exemplarCity>Fort Nelson</exemplarCity>
			</zone>
			<zone type="America/Dawson_Creek">
				<exemplarCity>Dawson Creek</exemplarCity>
			</zone>
			<zone type="America/Creston">
				<exemplarCity>Creston</exemplarCity>
			</zone>
			<zone type="America/Yellowknife">
				<exemplarCity>Yellowknife</exemplarCity>
			</zone>
			<zone type="America/Edmonton">
				<exemplarCity>Edmonton</exemplarCity>
			</zone>
			<zone type="America/Swift_Current">
				<exemplarCity>Swift Current</exemplarCity>
			</zone>
			<zone type="America/Cambridge_Bay">
				<exemplarCity>Cambridge Bay</exemplarCity>
			</zone>
			<zone type="America/Regina">
				<exemplarCity>Regina</exemplarCity>
			</zone>
			<zone type="America/Winnipeg">
				<exemplarCity>Winnipeg</exemplarCity>
			</zone>
			<zone type="America/Resolute">
				<exemplarCity>Resolute</exemplarCity>
			</zone>
			<zone type="America/Rainy_River">
				<exemplarCity>Rainy River</exemplarCity>
			</zone>
			<zone type="America/Rankin_Inlet">
				<exemplarCity>Rankin Inlet</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/Thunder_Bay">
				<exemplarCity>Thunder Bay</exemplarCity>
			</zone>
			<zone type="America/Nipigon">
				<exemplarCity>Nipigon</exemplarCity>
			</zone>
			<zone type="America/Toronto">
				<exemplarCity>Toronto</exemplarCity>
			</zone>
			<zone type="America/Iqaluit">
				<exemplarCity>Iqaluit</exemplarCity>
			</zone>
			<zone type="America/Pangnirtung">
				<exemplarCity>Pangnirtung</exemplarCity>
			</zone>
			<zone type="America/Moncton">
				<exemplarCity>Moncton</exemplarCity>
			</zone>
			<zone type="America/Halifax">
				<exemplarCity>Halifax</exemplarCity>
			</zone>
			<zone type="America/Goose_Bay">
				<exemplarCity>Goose Bay</exemplarCity>
			</zone>
			<zone type="America/Glace_Bay">
				<exemplarCity>Glace Bay</exemplarCity>
			</zone>
			<zone type="America/Blanc-Sablon">
				<exemplarCity>Blanc-Sablon</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>Saint John’s</exemplarCity>
			</zone>
			<zone type="Indian/Cocos">
				<exemplarCity>Cocos</exemplarCity>
			</zone>
			<zone type="Africa/Kinshasa">
				<exemplarCity>Kinshasa</exemplarCity>
			</zone>
			<zone type="Africa/Lubumbashi">
				<exemplarCity>Lubumbashi</exemplarCity>
			</zone>
			<zone type="Africa/Bangui">
				<exemplarCity>Bangui</exemplarCity>
			</zone>
			<zone type="Africa/Brazzaville">
				<exemplarCity>Brazzaville</exemplarCity>
			</zone>
			<zone type="Europe/Zurich">
				<exemplarCity>Zürich</exemplarCity>
			</zone>
			<zone type="Africa/Abidjan">
				<exemplarCity>Abidjan</exemplarCity>
			</zone>
			<zone type="Pacific/Rarotonga">
				<exemplarCity>Rarotonga</exemplarCity>
			</zone>
			<zone type="Pacific/Easter">
				<exemplarCity>Illa de Pascua</exemplarCity>
			</zone>
			<zone type="America/Punta_Arenas">
				<exemplarCity>Punta Arenas</exemplarCity>
			</zone>
			<zone type="America/Santiago">
				<exemplarCity>Santiago</exemplarCity>
			</zone>
			<zone type="Africa/Douala">
				<exemplarCity>Douala</exemplarCity>
			</zone>
			<zone type="Asia/Urumqi">
				<exemplarCity>Ürümqi</exemplarCity>
			</zone>
			<zone type="Asia/Shanghai">
				<exemplarCity>Shanghai</exemplarCity>
			</zone>
			<zone type="America/Bogota">
				<exemplarCity>Bogotá</exemplarCity>
			</zone>
			<zone type="America/Costa_Rica">
				<exemplarCity>Costa Rica</exemplarCity>
			</zone>
			<zone type="America/Havana">
				<exemplarCity>A Habana</exemplarCity>
			</zone>
			<zone type="Atlantic/Cape_Verde">
				<exemplarCity>Cabo Verde</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Indian/Christmas">
				<exemplarCity>Illa Christmas</exemplarCity>
			</zone>
			<zone type="Asia/Nicosia">
				<exemplarCity>Nicosia</exemplarCity>
			</zone>
			<zone type="Asia/Famagusta">
				<exemplarCity>Famagusta</exemplarCity>
			</zone>
			<zone type="Europe/Prague">
				<exemplarCity>Praga</exemplarCity>
			</zone>
			<zone type="Europe/Busingen">
				<exemplarCity>Busingen</exemplarCity>
			</zone>
			<zone type="Europe/Berlin">
				<exemplarCity>Berlín</exemplarCity>
			</zone>
			<zone type="Africa/Djibouti">
				<exemplarCity>Djibuti</exemplarCity>
			</zone>
			<zone type="Europe/Copenhagen">
				<exemplarCity>Copenhague</exemplarCity>
			</zone>
			<zone type="America/Dominica">
				<exemplarCity>Dominica</exemplarCity>
			</zone>
			<zone type="America/Santo_Domingo">
				<exemplarCity>Santo Domingo</exemplarCity>
			</zone>
			<zone type="Africa/Algiers">
				<exemplarCity>Alxer</exemplarCity>
			</zone>
			<zone type="Pacific/Galapagos">
				<exemplarCity>Illas Galápagos</exemplarCity>
			</zone>
			<zone type="America/Guayaquil">
				<exemplarCity>Guayaquil</exemplarCity>
			</zone>
			<zone type="Europe/Tallinn">
				<exemplarCity>Tallinn</exemplarCity>
			</zone>
			<zone type="Africa/Cairo">
				<exemplarCity>O Cairo</exemplarCity>
			</zone>
			<zone type="Africa/El_Aaiun">
				<exemplarCity>O Aiún</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmara</exemplarCity>
			</zone>
			<zone type="Atlantic/Canary">
				<exemplarCity>Illas Canarias</exemplarCity>
			</zone>
			<zone type="Africa/Ceuta">
				<exemplarCity>Ceuta</exemplarCity>
			</zone>
			<zone type="Europe/Madrid">
				<exemplarCity>Madrid</exemplarCity>
			</zone>
			<zone type="Africa/Addis_Ababa">
				<exemplarCity>Adís Abeba</exemplarCity>
			</zone>
			<zone type="Europe/Helsinki">
				<exemplarCity>Helsinqui</exemplarCity>
			</zone>
			<zone type="Pacific/Fiji">
				<exemplarCity>Fixi</exemplarCity>
			</zone>
			<zone type="Atlantic/Stanley">
				<exemplarCity>Stanley</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Pacific/Kosrae">
				<exemplarCity>Kosrae</exemplarCity>
			</zone>
			<zone type="Atlantic/Faeroe">
				<exemplarCity>Feroe</exemplarCity>
			</zone>
			<zone type="Europe/Paris">
				<exemplarCity>París</exemplarCity>
			</zone>
			<zone type="Africa/Libreville">
				<exemplarCity>Libreville</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<long>
					<daylight>Horario de verán británico</daylight>
				</long>
				<exemplarCity>Londres</exemplarCity>
			</zone>
			<zone type="America/Grenada">
				<exemplarCity>Granada</exemplarCity>
			</zone>
			<zone type="Asia/Tbilisi">
				<exemplarCity>Tbilisi</exemplarCity>
			</zone>
			<zone type="America/Cayenne">
				<exemplarCity>Caiena</exemplarCity>
			</zone>
			<zone type="Europe/Guernsey">
				<exemplarCity>Guernsey</exemplarCity>
			</zone>
			<zone type="Africa/Accra">
				<exemplarCity>Acra</exemplarCity>
			</zone>
			<zone type="Europe/Gibraltar">
				<exemplarCity>Xibraltar</exemplarCity>
			</zone>
			<zone type="America/Thule">
				<exemplarCity>Thule</exemplarCity>
			</zone>
			<zone type="America/Godthab">
				<exemplarCity>Nuuk</exemplarCity>
			</zone>
			<zone type="America/Scoresbysund">
				<exemplarCity>Ittoqqortoormiit</exemplarCity>
			</zone>
			<zone type="America/Danmarkshavn">
				<exemplarCity>Danmarkshavn</exemplarCity>
			</zone>
			<zone type="Africa/Banjul">
				<exemplarCity>Banjul</exemplarCity>
			</zone>
			<zone type="Africa/Conakry">
				<exemplarCity>Conakry</exemplarCity>
			</zone>
			<zone type="America/Guadeloupe">
				<exemplarCity>Guadalupe</exemplarCity>
			</zone>
			<zone type="Africa/Malabo">
				<exemplarCity>Malabo</exemplarCity>
			</zone>
			<zone type="Europe/Athens">
				<exemplarCity>Atenas</exemplarCity>
			</zone>
			<zone type="Atlantic/South_Georgia">
				<exemplarCity>Xeorxia do Sur</exemplarCity>
			</zone>
			<zone type="America/Guatemala">
				<exemplarCity>Guatemala</exemplarCity>
			</zone>
			<zone type="Pacific/Guam">
				<exemplarCity>Guam</exemplarCity>
			</zone>
			<zone type="Africa/Bissau">
				<exemplarCity>Bissau</exemplarCity>
			</zone>
			<zone type="America/Guyana">
				<exemplarCity>Güiana</exemplarCity>
			</zone>
			<zone type="Asia/Hong_Kong">
				<exemplarCity>Hong Kong</exemplarCity>
			</zone>
			<zone type="America/Tegucigalpa">
				<exemplarCity>Tegucigalpa</exemplarCity>
			</zone>
			<zone type="Europe/Zagreb">
				<exemplarCity>Zagreb</exemplarCity>
			</zone>
			<zone type="America/Port-au-Prince">
				<exemplarCity>Porto Príncipe</exemplarCity>
			</zone>
			<zone type="Europe/Budapest">
				<exemplarCity>Budapest</exemplarCity>
			</zone>
			<zone type="Asia/Jakarta">
				<exemplarCity>Iacarta</exemplarCity>
			</zone>
			<zone type="Asia/Pontianak">
				<exemplarCity>Pontianak</exemplarCity>
			</zone>
			<zone type="Asia/Makassar">
				<exemplarCity>Makassar</exemplarCity>
			</zone>
			<zone type="Asia/Jayapura">
				<exemplarCity>Jayapura</exemplarCity>
			</zone>
			<zone type="Europe/Dublin">
				<long>
					<daylight>Horario estándar irlandés</daylight>
				</long>
				<exemplarCity>Dublín</exemplarCity>
			</zone>
			<zone type="Asia/Jerusalem">
				<exemplarCity>Xerusalén</exemplarCity>
			</zone>
			<zone type="Europe/Isle_of_Man">
				<exemplarCity>Illa de Man</exemplarCity>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Calcuta</exemplarCity>
			</zone>
			<zone type="Indian/Chagos">
				<exemplarCity>Chagos</exemplarCity>
			</zone>
			<zone type="Asia/Baghdad">
				<exemplarCity>Bagdad</exemplarCity>
			</zone>
			<zone type="Asia/Tehran">
				<exemplarCity>Teherán</exemplarCity>
			</zone>
			<zone type="Atlantic/Reykjavik">
				<exemplarCity>Reiquiavik</exemplarCity>
			</zone>
			<zone type="Europe/Rome">
				<exemplarCity>Roma</exemplarCity>
			</zone>
			<zone type="Europe/Jersey">
				<exemplarCity>Jersey</exemplarCity>
			</zone>
			<zone type="America/Jamaica">
				<exemplarCity>Xamaica</exemplarCity>
			</zone>
			<zone type="Asia/Amman">
				<exemplarCity>Amán</exemplarCity>
			</zone>
			<zone type="Asia/Tokyo">
				<exemplarCity>Tokyo</exemplarCity>
			</zone>
			<zone type="Africa/Nairobi">
				<exemplarCity>Nairobi</exemplarCity>
			</zone>
			<zone type="Asia/Bishkek">
				<exemplarCity>Bishkek</exemplarCity>
			</zone>
			<zone type="Asia/Phnom_Penh">
				<exemplarCity>Phnom Penh</exemplarCity>
			</zone>
			<zone type="Pacific/Enderbury">
				<exemplarCity>Enderbury</exemplarCity>
			</zone>
			<zone type="Pacific/Kiritimati">
				<exemplarCity>Kiritimati</exemplarCity>
			</zone>
			<zone type="Pacific/Tarawa">
				<exemplarCity>Tarawa</exemplarCity>
			</zone>
			<zone type="Indian/Comoro">
				<exemplarCity>Comores</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>Saint Kitts</exemplarCity>
			</zone>
			<zone type="Asia/Pyongyang">
				<exemplarCity>Pyongyang</exemplarCity>
			</zone>
			<zone type="Asia/Seoul">
				<exemplarCity>Seúl</exemplarCity>
			</zone>
			<zone type="Asia/Kuwait">
				<exemplarCity>Kuwait</exemplarCity>
			</zone>
			<zone type="America/Cayman">
				<exemplarCity>Illas Caimán</exemplarCity>
			</zone>
			<zone type="Asia/Aqtau">
				<exemplarCity>Aktau</exemplarCity>
			</zone>
			<zone type="Asia/Oral">
				<exemplarCity>Oral</exemplarCity>
			</zone>
			<zone type="Asia/Atyrau">
				<exemplarCity>Atyrau</exemplarCity>
			</zone>
			<zone type="Asia/Aqtobe">
				<exemplarCity>Aktobe</exemplarCity>
			</zone>
			<zone type="Asia/Qostanay">
				<exemplarCity>Qostanai</exemplarCity>
			</zone>
			<zone type="Asia/Qyzylorda">
				<exemplarCity>Kyzylorda</exemplarCity>
			</zone>
			<zone type="Asia/Almaty">
				<exemplarCity>Almati</exemplarCity>
			</zone>
			<zone type="Asia/Vientiane">
				<exemplarCity>Vientiane</exemplarCity>
			</zone>
			<zone type="Asia/Beirut">
				<exemplarCity>Beirut</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>Santa Lucía</exemplarCity>
			</zone>
			<zone type="Europe/Vaduz">
				<exemplarCity>Vaduz</exemplarCity>
			</zone>
			<zone type="Asia/Colombo">
				<exemplarCity>Colombo</exemplarCity>
			</zone>
			<zone type="Africa/Monrovia">
				<exemplarCity>Monrovia</exemplarCity>
			</zone>
			<zone type="Africa/Maseru">
				<exemplarCity>Maseru</exemplarCity>
			</zone>
			<zone type="Europe/Vilnius">
				<exemplarCity>Vilnius</exemplarCity>
			</zone>
			<zone type="Europe/Luxembourg">
				<exemplarCity>Luxemburgo</exemplarCity>
			</zone>
			<zone type="Europe/Riga">
				<exemplarCity>Riga</exemplarCity>
			</zone>
			<zone type="Africa/Tripoli">
				<exemplarCity>Trípoli</exemplarCity>
			</zone>
			<zone type="Africa/Casablanca">
				<exemplarCity>Casablanca</exemplarCity>
			</zone>
			<zone type="Europe/Monaco">
				<exemplarCity>Mónaco</exemplarCity>
			</zone>
			<zone type="Europe/Chisinau">
				<exemplarCity>Chisinau</exemplarCity>
			</zone>
			<zone type="Europe/Podgorica">
				<exemplarCity>Podgorica</exemplarCity>
			</zone>
			<zone type="America/Marigot">
				<exemplarCity>Marigot</exemplarCity>
			</zone>
			<zone type="Indian/Antananarivo">
				<exemplarCity>Antananarivo</exemplarCity>
			</zone>
			<zone type="Pacific/Kwajalein">
				<exemplarCity>Kwajalein</exemplarCity>
			</zone>
			<zone type="Pacific/Majuro">
				<exemplarCity>Majuro</exemplarCity>
			</zone>
			<zone type="Europe/Skopje">
				<exemplarCity>Skopje</exemplarCity>
			</zone>
			<zone type="Africa/Bamako">
				<exemplarCity>Bamaco</exemplarCity>
			</zone>
			<zone type="Asia/Rangoon">
				<exemplarCity>Yangon</exemplarCity>
			</zone>
			<zone type="Asia/Hovd">
				<exemplarCity>Hovd</exemplarCity>
			</zone>
			<zone type="Asia/Ulaanbaatar">
				<exemplarCity>Ulaanbaatar</exemplarCity>
			</zone>
			<zone type="Asia/Choibalsan">
				<exemplarCity>Choibalsan</exemplarCity>
			</zone>
			<zone type="Asia/Macau">
				<exemplarCity>Macau</exemplarCity>
			</zone>
			<zone type="Pacific/Saipan">
				<exemplarCity>Saipan</exemplarCity>
			</zone>
			<zone type="America/Martinique">
				<exemplarCity>Martinica</exemplarCity>
			</zone>
			<zone type="Africa/Nouakchott">
				<exemplarCity>Nouakchott</exemplarCity>
			</zone>
			<zone type="America/Montserrat">
				<exemplarCity>Montserrat</exemplarCity>
			</zone>
			<zone type="Europe/Malta">
				<exemplarCity>Malta</exemplarCity>
			</zone>
			<zone type="Indian/Mauritius">
				<exemplarCity>Mauricio</exemplarCity>
			</zone>
			<zone type="Indian/Maldives">
				<exemplarCity>Maldivas</exemplarCity>
			</zone>
			<zone type="Africa/Blantyre">
				<exemplarCity>Blantyre</exemplarCity>
			</zone>
			<zone type="America/Tijuana">
				<exemplarCity>Tijuana</exemplarCity>
			</zone>
			<zone type="America/Hermosillo">
				<exemplarCity>Hermosillo</exemplarCity>
			</zone>
			<zone type="America/Mazatlan">
				<exemplarCity>Mazatlán</exemplarCity>
			</zone>
			<zone type="America/Chihuahua">
				<exemplarCity>Chihuahua</exemplarCity>
			</zone>
			<zone type="America/Bahia_Banderas">
				<exemplarCity>Bahía de Banderas</exemplarCity>
			</zone>
			<zone type="America/Ojinaga">
				<exemplarCity>Ojinaga</exemplarCity>
			</zone>
			<zone type="America/Monterrey">
				<exemplarCity>Monterrey</exemplarCity>
			</zone>
			<zone type="America/Mexico_City">
				<exemplarCity>Cidade de México</exemplarCity>
			</zone>
			<zone type="America/Matamoros">
				<exemplarCity>Matamoros</exemplarCity>
			</zone>
			<zone type="America/Merida">
				<exemplarCity>Mérida</exemplarCity>
			</zone>
			<zone type="America/Cancun">
				<exemplarCity>Cancún</exemplarCity>
			</zone>
			<zone type="Asia/Kuala_Lumpur">
				<exemplarCity>Kuala Lumpur</exemplarCity>
			</zone>
			<zone type="Asia/Kuching">
				<exemplarCity>Kuching</exemplarCity>
			</zone>
			<zone type="Africa/Maputo">
				<exemplarCity>Maputo</exemplarCity>
			</zone>
			<zone type="Africa/Windhoek">
				<exemplarCity>Windhoek</exemplarCity>
			</zone>
			<zone type="Pacific/Noumea">
				<exemplarCity>Noumea</exemplarCity>
			</zone>
			<zone type="Africa/Niamey">
				<exemplarCity>Niamey</exemplarCity>
			</zone>
			<zone type="Pacific/Norfolk">
				<exemplarCity>Norfolk</exemplarCity>
			</zone>
			<zone type="Africa/Lagos">
				<exemplarCity>Lagos</exemplarCity>
			</zone>
			<zone type="America/Managua">
				<exemplarCity>Managua</exemplarCity>
			</zone>
			<zone type="Europe/Amsterdam">
				<exemplarCity>Ámsterdam</exemplarCity>
			</zone>
			<zone type="Europe/Oslo">
				<exemplarCity>Oslo</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Katmandú</exemplarCity>
			</zone>
			<zone type="Pacific/Nauru">
				<exemplarCity>Nauru</exemplarCity>
			</zone>
			<zone type="Pacific/Niue">
				<exemplarCity>Niue</exemplarCity>
			</zone>
			<zone type="Pacific/Chatham">
				<exemplarCity>Chatham</exemplarCity>
			</zone>
			<zone type="Pacific/Auckland">
				<exemplarCity>Auckland</exemplarCity>
			</zone>
			<zone type="Asia/Muscat">
				<exemplarCity>Mascate</exemplarCity>
			</zone>
			<zone type="America/Panama">
				<exemplarCity>Panamá</exemplarCity>
			</zone>
			<zone type="America/Lima">
				<exemplarCity>Lima</exemplarCity>
			</zone>
			<zone type="Pacific/Tahiti">
				<exemplarCity>Tahití</exemplarCity>
			</zone>
			<zone type="Pacific/Marquesas">
				<exemplarCity>Marquesas</exemplarCity>
			</zone>
			<zone type="Pacific/Gambier">
				<exemplarCity>Gambier</exemplarCity>
			</zone>
			<zone type="Pacific/Port_Moresby">
				<exemplarCity>Port Moresby</exemplarCity>
			</zone>
			<zone type="Pacific/Bougainville">
				<exemplarCity>Bougainville</exemplarCity>
			</zone>
			<zone type="Asia/Manila">
				<exemplarCity>Manila</exemplarCity>
			</zone>
			<zone type="Asia/Karachi">
				<exemplarCity>Karachi</exemplarCity>
			</zone>
			<zone type="Europe/Warsaw">
				<exemplarCity>Varsovia</exemplarCity>
			</zone>
			<zone type="America/Miquelon">
				<exemplarCity>Miquelon</exemplarCity>
			</zone>
			<zone type="Pacific/Pitcairn">
				<exemplarCity>Pitcairn</exemplarCity>
			</zone>
			<zone type="America/Puerto_Rico">
				<exemplarCity>Porto Rico</exemplarCity>
			</zone>
			<zone type="Asia/Gaza">
				<exemplarCity>Gaza</exemplarCity>
			</zone>
			<zone type="Asia/Hebron">
				<exemplarCity>Hebrón</exemplarCity>
			</zone>
			<zone type="Atlantic/Azores">
				<exemplarCity>Azores</exemplarCity>
			</zone>
			<zone type="Atlantic/Madeira">
				<exemplarCity>Madeira</exemplarCity>
			</zone>
			<zone type="Europe/Lisbon">
				<exemplarCity>Lisboa</exemplarCity>
			</zone>
			<zone type="Pacific/Palau">
				<exemplarCity>Palau</exemplarCity>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Asunción</exemplarCity>
			</zone>
			<zone type="Asia/Qatar">
				<exemplarCity>Qatar</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Reunión</exemplarCity>
			</zone>
			<zone type="Europe/Bucharest">
				<exemplarCity>Bucarest</exemplarCity>
			</zone>
			<zone type="Europe/Belgrade">
				<exemplarCity>Belgrado</exemplarCity>
			</zone>
			<zone type="Europe/Kaliningrad">
				<exemplarCity>Kaliningrado</exemplarCity>
			</zone>
			<zone type="Europe/Moscow">
				<exemplarCity>Moscova</exemplarCity>
			</zone>
			<zone type="Europe/Volgograd">
				<exemplarCity>Volgogrado</exemplarCity>
			</zone>
			<zone type="Europe/Saratov">
				<exemplarCity>Saratov</exemplarCity>
			</zone>
			<zone type="Europe/Astrakhan">
				<exemplarCity>Astrakán</exemplarCity>
			</zone>
			<zone type="Europe/Ulyanovsk">
				<exemplarCity>Ulianovsk</exemplarCity>
			</zone>
			<zone type="Europe/Kirov">
				<exemplarCity>Kirov</exemplarCity>
			</zone>
			<zone type="Europe/Samara">
				<exemplarCity>Samara</exemplarCity>
			</zone>
			<zone type="Asia/Yekaterinburg">
				<exemplarCity>Ekaterinburgo</exemplarCity>
			</zone>
			<zone type="Asia/Omsk">
				<exemplarCity>Omsk</exemplarCity>
			</zone>
			<zone type="Asia/Novosibirsk">
				<exemplarCity>Novosibirsk</exemplarCity>
			</zone>
			<zone type="Asia/Barnaul">
				<exemplarCity>Barnaul</exemplarCity>
			</zone>
			<zone type="Asia/Tomsk">
				<exemplarCity>Tomsk</exemplarCity>
			</zone>
			<zone type="Asia/Novokuznetsk">
				<exemplarCity>Novokuznetsk</exemplarCity>
			</zone>
			<zone type="Asia/Krasnoyarsk">
				<exemplarCity>Krasnoyarsk</exemplarCity>
			</zone>
			<zone type="Asia/Irkutsk">
				<exemplarCity>Irkutsk</exemplarCity>
			</zone>
			<zone type="Asia/Chita">
				<exemplarCity>Chitá</exemplarCity>
			</zone>
			<zone type="Asia/Yakutsk">
				<exemplarCity>Iakutsk</exemplarCity>
			</zone>
			<zone type="Asia/Vladivostok">
				<exemplarCity>Vladivostok</exemplarCity>
			</zone>
			<zone type="Asia/Khandyga">
				<exemplarCity>Chandyga</exemplarCity>
			</zone>
			<zone type="Asia/Sakhalin">
				<exemplarCity>Sakhalín</exemplarCity>
			</zone>
			<zone type="Asia/Ust-Nera">
				<exemplarCity>Ust-Nera</exemplarCity>
			</zone>
			<zone type="Asia/Magadan">
				<exemplarCity>Magadan</exemplarCity>
			</zone>
			<zone type="Asia/Srednekolymsk">
				<exemplarCity>Srednekolimsk</exemplarCity>
			</zone>
			<zone type="Asia/Kamchatka">
				<exemplarCity>Kamchatka</exemplarCity>
			</zone>
			<zone type="Asia/Anadyr">
				<exemplarCity>Anadyr</exemplarCity>
			</zone>
			<zone type="Africa/Kigali">
				<exemplarCity>Kigali</exemplarCity>
			</zone>
			<zone type="Asia/Riyadh">
				<exemplarCity>Riad</exemplarCity>
			</zone>
			<zone type="Pacific/Guadalcanal">
				<exemplarCity>Guadalcanal</exemplarCity>
			</zone>
			<zone type="Indian/Mahe">
				<exemplarCity>Mahé</exemplarCity>
			</zone>
			<zone type="Africa/Khartoum">
				<exemplarCity>Khartún</exemplarCity>
			</zone>
			<zone type="Europe/Stockholm">
				<exemplarCity>Estocolmo</exemplarCity>
			</zone>
			<zone type="Asia/Singapore">
				<exemplarCity>Singapur</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>Santa Helena</exemplarCity>
			</zone>
			<zone type="Europe/Ljubljana">
				<exemplarCity>Liubliana</exemplarCity>
			</zone>
			<zone type="Arctic/Longyearbyen">
				<exemplarCity>Longyearbyen</exemplarCity>
			</zone>
			<zone type="Europe/Bratislava">
				<exemplarCity>Bratislava</exemplarCity>
			</zone>
			<zone type="Africa/Freetown">
				<exemplarCity>Freetown</exemplarCity>
			</zone>
			<zone type="Europe/San_Marino">
				<exemplarCity>San Marino</exemplarCity>
			</zone>
			<zone type="Africa/Dakar">
				<exemplarCity>Dakar</exemplarCity>
			</zone>
			<zone type="Africa/Mogadishu">
				<exemplarCity>Mogadixo</exemplarCity>
			</zone>
			<zone type="America/Paramaribo">
				<exemplarCity>Paramaribo</exemplarCity>
			</zone>
			<zone type="Africa/Juba">
				<exemplarCity>Juba</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>San Tomé</exemplarCity>
			</zone>
			<zone type="America/El_Salvador">
				<exemplarCity>O Salvador</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince’s Quarter</exemplarCity>
			</zone>
			<zone type="Asia/Damascus">
				<exemplarCity>Damasco</exemplarCity>
			</zone>
			<zone type="Africa/Mbabane">
				<exemplarCity>Mbabane</exemplarCity>
			</zone>
			<zone type="America/Grand_Turk">
				<exemplarCity>Grand Turk</exemplarCity>
			</zone>
			<zone type="Africa/Ndjamena">
				<exemplarCity>N’Djamena</exemplarCity>
			</zone>
			<zone type="Indian/Kerguelen">
				<exemplarCity>Kerguelen</exemplarCity>
			</zone>
			<zone type="Africa/Lome">
				<exemplarCity>Lomé</exemplarCity>
			</zone>
			<zone type="Asia/Bangkok">
				<exemplarCity>Bangkok</exemplarCity>
			</zone>
			<zone type="Asia/Dushanbe">
				<exemplarCity>Dushanbe</exemplarCity>
			</zone>
			<zone type="Pacific/Fakaofo">
				<exemplarCity>Fakaofo</exemplarCity>
			</zone>
			<zone type="Asia/Dili">
				<exemplarCity>Dili</exemplarCity>
			</zone>
			<zone type="Asia/Ashgabat">
				<exemplarCity>Achkhabad</exemplarCity>
			</zone>
			<zone type="Africa/Tunis">
				<exemplarCity>Tunes</exemplarCity>
			</zone>
			<zone type="Pacific/Tongatapu">
				<exemplarCity>Tongatapu</exemplarCity>
			</zone>
			<zone type="Europe/Istanbul">
				<exemplarCity>Istanbul</exemplarCity>
			</zone>
			<zone type="America/Port_of_Spain">
				<exemplarCity>Porto España</exemplarCity>
			</zone>
			<zone type="Pacific/Funafuti">
				<exemplarCity>Funafuti</exemplarCity>
			</zone>
			<zone type="Asia/Taipei">
				<exemplarCity>Taipei</exemplarCity>
			</zone>
			<zone type="Africa/Dar_es_Salaam">
				<exemplarCity>Dar es Salaam</exemplarCity>
			</zone>
			<zone type="Europe/Uzhgorod">
				<exemplarCity>Uzghorod</exemplarCity>
			</zone>
			<zone type="Europe/Kiev">
				<exemplarCity>Kiev</exemplarCity>
			</zone>
			<zone type="Europe/Simferopol">
				<exemplarCity>Simferópol</exemplarCity>
			</zone>
			<zone type="Europe/Zaporozhye">
				<exemplarCity>Zaporizhia</exemplarCity>
			</zone>
			<zone type="Africa/Kampala">
				<exemplarCity>Kampala</exemplarCity>
			</zone>
			<zone type="Pacific/Midway">
				<exemplarCity>Midway</exemplarCity>
			</zone>
			<zone type="Pacific/Wake">
				<exemplarCity>Wake</exemplarCity>
			</zone>
			<zone type="America/Adak">
				<exemplarCity>Adak</exemplarCity>
			</zone>
			<zone type="America/Nome">
				<exemplarCity>Nome</exemplarCity>
			</zone>
			<zone type="Pacific/Honolulu">
				<exemplarCity>Honolulú</exemplarCity>
			</zone>
			<zone type="Pacific/Johnston">
				<exemplarCity>Johnston</exemplarCity>
			</zone>
			<zone type="America/Anchorage">
				<exemplarCity>Anchorage</exemplarCity>
			</zone>
			<zone type="America/Yakutat">
				<exemplarCity>Yakutat</exemplarCity>
			</zone>
			<zone type="America/Sitka">
				<exemplarCity>Sitka</exemplarCity>
			</zone>
			<zone type="America/Juneau">
				<exemplarCity>Juneau</exemplarCity>
			</zone>
			<zone type="America/Metlakatla">
				<exemplarCity>Metlakatla</exemplarCity>
			</zone>
			<zone type="America/Los_Angeles">
				<exemplarCity>Os Ánxeles</exemplarCity>
			</zone>
			<zone type="America/Boise">
				<exemplarCity>Boise</exemplarCity>
			</zone>
			<zone type="America/Phoenix">
				<exemplarCity>Phoenix</exemplarCity>
			</zone>
			<zone type="America/Denver">
				<exemplarCity>Denver</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, Dacota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salem, Dacota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, Dacota do Norte</exemplarCity>
			</zone>
			<zone type="America/Chicago">
				<exemplarCity>Chicago</exemplarCity>
			</zone>
			<zone type="America/Menominee">
				<exemplarCity>Menominee</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburg, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indianapolis">
				<exemplarCity>Indianápolis</exemplarCity>
			</zone>
			<zone type="America/Louisville">
				<exemplarCity>Louisville</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="America/Detroit">
				<exemplarCity>Detroit</exemplarCity>
			</zone>
			<zone type="America/New_York">
				<exemplarCity>Nova York</exemplarCity>
			</zone>
			<zone type="America/Montevideo">
				<exemplarCity>Montevideo</exemplarCity>
			</zone>
			<zone type="Asia/Samarkand">
				<exemplarCity>Samarcanda</exemplarCity>
			</zone>
			<zone type="Asia/Tashkent">
				<exemplarCity>Tashkent</exemplarCity>
			</zone>
			<zone type="Europe/Vatican">
				<exemplarCity>Vaticano</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>San Vicente</exemplarCity>
			</zone>
			<zone type="America/Caracas">
				<exemplarCity>Caracas</exemplarCity>
			</zone>
			<zone type="America/Tortola">
				<exemplarCity>Tórtola</exemplarCity>
			</zone>
			<zone type="America/St_Thomas">
				<exemplarCity>Saint Thomas</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Ho Chi Minh</exemplarCity>
			</zone>
			<zone type="Pacific/Efate">
				<exemplarCity>Efate</exemplarCity>
			</zone>
			<zone type="Pacific/Wallis">
				<exemplarCity>Wallis</exemplarCity>
			</zone>
			<zone type="Pacific/Apia">
				<exemplarCity>Apia</exemplarCity>
			</zone>
			<zone type="Asia/Aden">
				<exemplarCity>Adén</exemplarCity>
			</zone>
			<zone type="Indian/Mayotte">
				<exemplarCity>Mayotte</exemplarCity>
			</zone>
			<zone type="Africa/Johannesburg">
				<exemplarCity>Xohanesburgo</exemplarCity>
			</zone>
			<zone type="Africa/Lusaka">
				<exemplarCity>Lusaca</exemplarCity>
			</zone>
			<zone type="Africa/Harare">
				<exemplarCity>Harare</exemplarCity>
			</zone>
			<metazone type="Afghanistan">
				<long>
					<standard>Horario de Afganistán</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard>Horario de África Central</standard>
				</long>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard>Horario de África Oriental</standard>
				</long>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>Horario de África Meridional</standard>
				</long>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>Horario de África Occidental</generic>
					<standard>Horario estándar de África Occidental</standard>
					<daylight>Horario de verán de África Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Horario de Alasca</generic>
					<standard>Horario estándar de Alasca</standard>
					<daylight>Horario de verán de Alasca</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Horario do Amazonas</generic>
					<standard>Horario estándar do Amazonas</standard>
					<daylight>Horario de verán do Amazonas</daylight>
				</long>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Horario central, Norteamérica</generic>
					<standard>Horario estándar central, Norteamérica</standard>
					<daylight>Horario de verán central, Norteamérica</daylight>
				</long>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Horario do leste, América do Norte</generic>
					<standard>Horario estándar do leste, América do Norte</standard>
					<daylight>Horario de verán do leste, América do Norte</daylight>
				</long>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Horario da montaña, América do Norte</generic>
					<standard>Horario estándar da montaña, América do Norte</standard>
					<daylight>Horario de verán da montaña, América do Norte</daylight>
				</long>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Horario do Pacífico, América do Norte</generic>
					<standard>Horario estándar do Pacífico, América do Norte</standard>
					<daylight>Horario de verán do Pacífico, América do Norte</daylight>
				</long>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Horario de Anadir</generic>
					<standard>Horario estándar de Anadir</standard>
					<daylight>Horario de verán de Anadir</daylight>
				</long>
			</metazone>
			<metazone type="Apia">
				<long>
					<generic>Horario de Apia</generic>
					<standard>Horario estándar de Apia</standard>
					<daylight>Horario de verán de Apia</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Horario árabe</generic>
					<standard>Horario estándar árabe</standard>
					<daylight>Horario de verán árabe</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Horario da Arxentina</generic>
					<standard>Horario estándar da Arxentina</standard>
					<daylight>Horario de verán da Arxentina</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Horario da Arxentina Occidental</generic>
					<standard>Horario estándar da Arxentina Occidental</standard>
					<daylight>Horario de verán da Arxentina Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Horario de Armenia</generic>
					<standard>Horario estándar de Armenia</standard>
					<daylight>Horario de verán de Armenia</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Horario do Atlántico</generic>
					<standard>Horario estándar do Atlántico</standard>
					<daylight>Horario de verán do Atlántico</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Horario de Australia Central</generic>
					<standard>Horario estándar de Australia Central</standard>
					<daylight>Horario de verán de Australia Central</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Horario de Australia Occidental Central</generic>
					<standard>Horario estándar de Australia Occidental Central</standard>
					<daylight>Horario de verán de Australia Occidental Central</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Horario de Australia Oriental</generic>
					<standard>Horario estándar de Australia Oriental</standard>
					<daylight>Horario de verán de Australia Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Horario de Australia Occidental</generic>
					<standard>Horario estándar de Australia Occidental</standard>
					<daylight>Horario de verán de Australia Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Horario de Acerbaixán</generic>
					<standard>Horario estándar de Acerbaixán</standard>
					<daylight>Horario de verán de Acerbaixán</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Horario dos Azores</generic>
					<standard>Horario estándar dos Azores</standard>
					<daylight>Horario de verán dos Azores</daylight>
				</long>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Horario de Bangladesh</generic>
					<standard>Horario estándar de Bangladesh</standard>
					<daylight>Horario de verán de Bangladesh</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Horario de Bután</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Horario de Bolivia</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Horario de Brasilia</generic>
					<standard>Horario estándar de Brasilia</standard>
					<daylight>Horario de verán de Brasilia</daylight>
				</long>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Horario de Brunei Darussalam</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic>Horario de Cabo Verde</generic>
					<standard>Horario estándar de Cabo Verde</standard>
					<daylight>Horario de verán de Cabo Verde</daylight>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Horario estándar chamorro</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Horario de Chatham</generic>
					<standard>Horario estándar de Chatham</standard>
					<daylight>Horario de verán de Chatham</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Horario de Chile</generic>
					<standard>Horario estándar de Chile</standard>
					<daylight>Horario de verán de Chile</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>Horario da China</generic>
					<standard>Horario estándar da China</standard>
					<daylight>Horario de verán da China</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Horario de Choibalsan</generic>
					<standard>Horario estándar de Choibalsan</standard>
					<daylight>Horario de verán de Choibalsan</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Horario da Illa Christmas</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Horario das Illas Cocos</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Horario de Colombia</generic>
					<standard>Horario estándar de Colombia</standard>
					<daylight>Horario de verán de Colombia</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Horario das Illas Cook</generic>
					<standard>Horario estándar das Illas Cook</standard>
					<daylight>Horario de verán medio das Illas Cook</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic>Horario de Cuba</generic>
					<standard>Horario estándar de Cuba</standard>
					<daylight>Horario de verán de Cuba</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Horario de Davis</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Horario de Dumont-d’Urville</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>Horario de Timor Leste</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Horario da Illa de Pascua</generic>
					<standard>Horario estándar da Illa de Pascua</standard>
					<daylight>Horario de verán da Illa de Pascua</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Horario de Ecuador</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Horario de Europa Central</generic>
					<standard>Horario estándar de Europa Central</standard>
					<daylight>Horario de verán de Europa Central</daylight>
				</long>
				<short>
					<generic>CET</generic>
					<standard>CET</standard>
					<daylight>CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Horario de Europa Oriental</generic>
					<standard>Horario estándar de Europa Oriental</standard>
					<daylight>Horario de verán de Europa Oriental</daylight>
				</long>
				<short>
					<generic>EET</generic>
					<standard>EET</standard>
					<daylight>EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Further_Eastern">
				<long>
					<standard>Horario do extremo leste europeo</standard>
				</long>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Horario de Europa Occidental</generic>
					<standard>Horario estándar de Europa Occidental</standard>
					<daylight>Horario de verán de Europa Occidental</daylight>
				</long>
				<short>
					<generic>WET</generic>
					<standard>WET</standard>
					<daylight>WEST</daylight>
				</short>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Horario das Illas Malvinas</generic>
					<standard>Horario estándar das Illas Malvinas</standard>
					<daylight>Horario de verán das Illas Malvinas</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Horario de Fixi</generic>
					<standard>Horario estándar de Fixi</standard>
					<daylight>Horario de verán de Fixi</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>Horario da Güiana Francesa</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>Horario das Terras Austrais e Antárticas Francesas</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Horario das Galápagos</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Horario de Gambier</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Horario de Xeorxia</generic>
					<standard>Horario estándar de Xeorxia</standard>
					<daylight>Horario de verán de Xeorxia</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Horario das Illas Gilbert</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Horario do meridiano de Greenwich</standard>
				</long>
				<short>
					<standard>GMT</standard>
				</short>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>Horario de Groenlandia Oriental</generic>
					<standard>Horario estándar de Groenlandia Oriental</standard>
					<daylight>Horario de verán de Groenlandia Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>Horario de Groenlandia Occidental</generic>
					<standard>Horario estándar de Groenlandia Occidental</standard>
					<daylight>Horario de verán de Groenlandia Occidental</daylight>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Horario do Golfo</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Horario da Güiana</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Horario de Hawai-illas Aleutianas</generic>
					<standard>Horario estándar de Hawai-illas Aleutianas</standard>
					<daylight>Horario de verán de Hawai-illas Aleutianas</daylight>
				</long>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Horario de Hong Kong</generic>
					<standard>Horario estándar de Hong Kong</standard>
					<daylight>Horario de verán de Hong Kong</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Horario de Hovd</generic>
					<standard>Horario estándar de Hovd</standard>
					<daylight>Horario de verán de Hovd</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>Horario da India</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Horario do Océano Índico</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Horario de Indochina</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Horario de Indonesia Central</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Horario de Indonesia Oriental</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Horario de Indonesia Occidental</standard>
				</long>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Horario de Irán</generic>
					<standard>Horario estándar de Irán</standard>
					<daylight>Horario de verán de Irán</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Horario de Irkutsk</generic>
					<standard>Horario estándar de Irkutsk</standard>
					<daylight>Horario de verán de Irkutsk</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Horario de Israel</generic>
					<standard>Horario estándar de Israel</standard>
					<daylight>Horario de verán de Israel</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Horario do Xapón</generic>
					<standard>Horario estándar do Xapón</standard>
					<daylight>Horario de verán do Xapón</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic>Horario de Petropávlovsk-Kamchatski</generic>
					<standard>Horario estándar de Petropávlovsk-Kamchatski</standard>
					<daylight>Horario de verán de Petropávlovsk-Kamchatski</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>Horario de Kazakistán Oriental</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>Horario de Kazakistán Occidental</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Horario de Corea</generic>
					<standard>Horario estándar de Corea</standard>
					<daylight>Horario de verán de Corea</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Horario de Kosrae</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Horario de Krasnoiarsk</generic>
					<standard>Horario estándar de Krasnoiarsk</standard>
					<daylight>Horario de verán de Krasnoiarsk</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Horario de Kirguizistán</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Horario das Illas da Liña</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Horario de Lord Howe</generic>
					<standard>Horario estándar de Lord Howe</standard>
					<daylight>Horario de verán de Lord Howe</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Horario da Illa Macquarie</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Horario de Magadan</generic>
					<standard>Horario estándar de Magadan</standard>
					<daylight>Horario de verán de Magadan</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Horario de Malaisia</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Horario das Maldivas</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Horario das Marquesas</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Horario das Illas Marshall</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Horario de Mauricio</generic>
					<standard>Horario estándar de Mauricio</standard>
					<daylight>Horario de verán de Mauricio</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Horario de Mawson</standard>
				</long>
			</metazone>
			<metazone type="Mexico_Northwest">
				<long>
					<generic>Horario do noroeste de México</generic>
					<standard>Horario estándar do noroeste de México</standard>
					<daylight>Horario de verán do noroeste de México</daylight>
				</long>
			</metazone>
			<metazone type="Mexico_Pacific">
				<long>
					<generic>Horario do Pacífico mexicano</generic>
					<standard>Horario estándar do Pacífico mexicano</standard>
					<daylight>Horario de verán do Pacífico mexicano</daylight>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Horario de Ulaanbaatar</generic>
					<standard>Horario estándar de Ulaanbaatar</standard>
					<daylight>Horario de verán de Ulaanbaatar</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Horario de Moscova</generic>
					<standard>Horario estándar de Moscova</standard>
					<daylight>Horario de verán de Moscova</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Horario de Myanmar</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Horario de Nauru</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Horario de Nepal</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>Horario de Nova Caledonia</generic>
					<standard>Horario estándar de Nova Caledonia</standard>
					<daylight>Horario de verán de Nova Caledonia</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>Horario de Nova Zelandia</generic>
					<standard>Horario estándar de Nova Zelandia</standard>
					<daylight>Horario de verán de Nova Zelandia</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Horario de Terra Nova</generic>
					<standard>Horario estándar de Terra Nova</standard>
					<daylight>Horario de verán de Terra Nova</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Horario de Niue</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<generic>Horario da Illa Norfolk</generic>
					<standard>Horario estándar da Illa Norfolk</standard>
					<daylight>Horario de verán da Illa Norfolk</daylight>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Horario de Fernando de Noronha</generic>
					<standard>Horario estándar de Fernando de Noronha</standard>
					<daylight>Horario de verán de Fernando de Noronha</daylight>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Horario de Novosibirsk</generic>
					<standard>Horario estándar de Novosibirsk</standard>
					<daylight>Horario de verán de Novosibirsk</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Horario de Omsk</generic>
					<standard>Horario estándar de Omsk</standard>
					<daylight>Horario de verán de Omsk</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Horario de Paquistán</generic>
					<standard>Horario estándar de Paquistán</standard>
					<daylight>Horario de verán de Paquistán</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Horario de Palau</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Horario de Papúa-Nova Guinea</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Horario do Paraguai</generic>
					<standard>Horario estándar do Paraguai</standard>
					<daylight>Horario de verán do Paraguai</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Horario do Perú</generic>
					<standard>Horario estándar do Perú</standard>
					<daylight>Horario de verán do Perú</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Horario de Filipinas</generic>
					<standard>Horario estándar de Filipinas</standard>
					<daylight>Horario de verán de Filipinas</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Horario das Illas Fénix</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Horario de Saint Pierre et Miquelon</generic>
					<standard>Horario estándar de Saint Pierre et Miquelon</standard>
					<daylight>Horario de verán de Saint Pierre et Miquelon</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Horario de Pitcairn</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Horario de Pohnpei</standard>
				</long>
			</metazone>
			<metazone type="Pyongyang">
				<long>
					<standard>Horario de Pyongyang</standard>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Horario de Reunión</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Horario de Rothera</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Horario de Sakhalín</generic>
					<standard>Horario estándar de Sakhalín</standard>
					<daylight>Horario de verán de Sakhalín</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic>Horario de Samara</generic>
					<standard>Horario estándar de Samara</standard>
					<daylight>Horario de verán de Samara</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Horario de Samoa</generic>
					<standard>Horario estándar de Samoa</standard>
					<daylight>Horario de verán de Samoa</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Horario das Seychelles</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Horario de Singapur</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Horario das Illas Salomón</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>Horario de Xeorxia do Sur</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Horario de Suriname</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Horario de Syowa</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Horario de Tahití</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Horario de Taipei</generic>
					<standard>Horario estándar de Taipei</standard>
					<daylight>Horario de verán de Taipei</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Horario de Taxiquistán</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Horario de Tokelau</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Horario de Tonga</generic>
					<standard>Horario estándar de Tonga</standard>
					<daylight>Horario de verán de Tonga</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Horario de Chuuk</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Horario de Turkmenistán</generic>
					<standard>Horario estándar de Turkmenistán</standard>
					<daylight>Horario de verán de Turkmenistán</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Horario de Tuvalu</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Horario do Uruguai</generic>
					<standard>Horario estándar do Uruguai</standard>
					<daylight>Horario de verán do Uruguai</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Horario de Uzbekistán</generic>
					<standard>Horario estándar de Uzbekistán</standard>
					<daylight>Horario de verán de Uzbekistán</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Horario de Vanuatu</generic>
					<standard>Horario estándar de Vanuatu</standard>
					<daylight>Horario de verán de Vanuatu</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Horario de Venezuela</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Horario de Vladivostok</generic>
					<standard>Horario estándar de Vladivostok</standard>
					<daylight>Horario de verán de Vladivostok</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Horario de Volgogrado</generic>
					<standard>Horario estándar de Volgogrado</standard>
					<daylight>Horario de verán de Volgogrado</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Horario de Vostok</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Horario da Illa Wake</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Horario de Wallis e Futuna</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Horario de Iakutsk</generic>
					<standard>Horario estándar de Iakutsk</standard>
					<daylight>Horario de verán de Iakutsk</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Horario de Ekaterimburgo</generic>
					<standard>Horario estándar de Ekaterimburgo</standard>
					<daylight>Horario de verán de Ekaterimburgo</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<defaultNumberingSystem draft="contributed">latn</defaultNumberingSystem>
		<otherNumberingSystems>
			<native draft="contributed">latn</native>
		</otherNumberingSystems>
		<minimumGroupingDigits draft="contributed">1</minimumGroupingDigits>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group>.</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign draft="contributed">+</plusSign>
			<minusSign draft="contributed">-</minusSign>
			<approximatelySign>~</approximatelySign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
			<timeSeparator draft="contributed">:</timeSeparator>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern draft="contributed">#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one">0</pattern>
					<pattern type="1000" count="other">0</pattern>
					<pattern type="10000" count="one">0</pattern>
					<pattern type="10000" count="other">0</pattern>
					<pattern type="100000" count="one">0</pattern>
					<pattern type="100000" count="other">0</pattern>
					<pattern type="1000000" count="one">0 millón</pattern>
					<pattern type="1000000" count="other">0 millóns</pattern>
					<pattern type="********" count="one">00 millóns</pattern>
					<pattern type="********" count="other">00 millóns</pattern>
					<pattern type="*********" count="one">000 millóns</pattern>
					<pattern type="*********" count="other">000 millóns</pattern>
					<pattern type="*********0" count="one">0000 millóns</pattern>
					<pattern type="*********0" count="other">0000 millóns</pattern>
					<pattern type="*********00" count="one">00000 millóns</pattern>
					<pattern type="*********00" count="other">00000 millóns</pattern>
					<pattern type="*********000" count="one">000000 millóns</pattern>
					<pattern type="*********000" count="other">000000 millóns</pattern>
					<pattern type="*********0000" count="one">0 billón</pattern>
					<pattern type="*********0000" count="other">0 billóns</pattern>
					<pattern type="*********00000" count="one">00 billóns</pattern>
					<pattern type="*********00000" count="other">00 billóns</pattern>
					<pattern type="*********000000" count="one">000 billóns</pattern>
					<pattern type="*********000000" count="other">000 billóns</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one">0</pattern>
					<pattern type="1000" count="other">0</pattern>
					<pattern type="10000" count="one">0</pattern>
					<pattern type="10000" count="other">0</pattern>
					<pattern type="100000" count="one">0</pattern>
					<pattern type="100000" count="other">0</pattern>
					<pattern type="1000000" count="one">0 M</pattern>
					<pattern type="1000000" count="other">0 M</pattern>
					<pattern type="********" count="one">00 M</pattern>
					<pattern type="********" count="other">00 M</pattern>
					<pattern type="*********" count="one">000 M</pattern>
					<pattern type="*********" count="other">000 M</pattern>
					<pattern type="*********0" count="one">0000 M</pattern>
					<pattern type="*********0" count="other">0000 M</pattern>
					<pattern type="*********00" count="one">00000 M</pattern>
					<pattern type="*********00" count="other">00000 M</pattern>
					<pattern type="*********000" count="one">000000 M</pattern>
					<pattern type="*********000" count="other">000000 M</pattern>
					<pattern type="*********0000" count="one">0 B</pattern>
					<pattern type="*********0000" count="other">0 B</pattern>
					<pattern type="*********00000" count="one">00 B</pattern>
					<pattern type="*********00000" count="other">00 B</pattern>
					<pattern type="*********000000" count="one">000 B</pattern>
					<pattern type="*********000000" count="other">000 B</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0 %</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>#,##0.00 ¤</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>#,##0.00 ¤</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<currencyFormatLength type="short">
				<currencyFormat type="standard">
					<pattern type="1000" count="one">0</pattern>
					<pattern type="1000" count="other">0</pattern>
					<pattern type="10000" count="one">0</pattern>
					<pattern type="10000" count="other">0</pattern>
					<pattern type="100000" count="one">0</pattern>
					<pattern type="100000" count="other">0</pattern>
					<pattern type="1000000" count="one">0 M¤</pattern>
					<pattern type="1000000" count="other">0 M¤</pattern>
					<pattern type="********" count="one">00 M¤</pattern>
					<pattern type="********" count="other">00 M¤</pattern>
					<pattern type="*********" count="one">000 M¤</pattern>
					<pattern type="*********" count="other">000 M¤</pattern>
					<pattern type="*********0" count="one">0000 M¤</pattern>
					<pattern type="*********0" count="other">0000 M¤</pattern>
					<pattern type="*********00" count="one">00000 M¤</pattern>
					<pattern type="*********00" count="other">00000 M¤</pattern>
					<pattern type="*********000" count="one">00000 M¤</pattern>
					<pattern type="*********000" count="other">00000 M¤</pattern>
					<pattern type="*********0000" count="one">0 B¤</pattern>
					<pattern type="*********0000" count="other">0 B¤</pattern>
					<pattern type="*********00000" count="one">00 B¤</pattern>
					<pattern type="*********00000" count="other">00 B¤</pattern>
					<pattern type="*********000000" count="one">000 B¤</pattern>
					<pattern type="*********000000" count="other">000 B¤</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="ADP">
				<displayName>peseta andorrana</displayName>
				<displayName count="one" draft="contributed">peseta andorrana</displayName>
				<displayName count="other" draft="contributed">pesetas andorranas</displayName>
			</currency>
			<currency type="AED">
				<displayName>dirham dos Emiratos Árabes Unidos</displayName>
				<displayName count="one">dirham dos Emiratos Árabes Unidos</displayName>
				<displayName count="other">dirhams dos Emiratos Árabes Unidos</displayName>
				<symbol draft="contributed">AED</symbol>
			</currency>
			<currency type="AFN">
				<displayName>afgani afgán</displayName>
				<displayName count="one">afgani afgán</displayName>
				<displayName count="other">afganis afgáns</displayName>
				<symbol draft="contributed">AFN</symbol>
			</currency>
			<currency type="ALL">
				<displayName>lek albanés</displayName>
				<displayName count="one">lek albanés</displayName>
				<displayName count="other">lekë albaneses</displayName>
				<symbol draft="contributed">ALL</symbol>
			</currency>
			<currency type="AMD">
				<displayName>dram armenio</displayName>
				<displayName count="one">dram armenio</displayName>
				<displayName count="other">drams armenios</displayName>
				<symbol draft="contributed">AMD</symbol>
			</currency>
			<currency type="ANG">
				<displayName>florín das Antillas Neerlandesas</displayName>
				<displayName count="one">florín das Antillas Neerlandesas</displayName>
				<displayName count="other">floríns das Antillas Neerlandesas</displayName>
				<symbol draft="contributed">ANG</symbol>
			</currency>
			<currency type="AOA">
				<displayName>kwanza angolano</displayName>
				<displayName count="one">kwanza angolano</displayName>
				<displayName count="other">kwanzas angolanos</displayName>
				<symbol draft="contributed">AOA</symbol>
				<symbol alt="narrow" draft="contributed">Kz</symbol>
			</currency>
			<currency type="ARP">
				<displayName draft="contributed">Peso arxentino (1983–1985)</displayName>
				<displayName count="one" draft="contributed">peso arxentino (ARP)</displayName>
				<displayName count="other" draft="contributed">pesos arxentinos (ARP)</displayName>
			</currency>
			<currency type="ARS">
				<displayName>peso arxentino</displayName>
				<displayName count="one">peso arxentino</displayName>
				<displayName count="other">pesos arxentinos</displayName>
				<symbol draft="contributed">ARS</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="AUD">
				<displayName>dólar australiano</displayName>
				<displayName count="one">dólar australiano</displayName>
				<displayName count="other">dólares australianos</displayName>
				<symbol draft="contributed">A$</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="AWG">
				<displayName>florín de Aruba</displayName>
				<displayName count="one">florín de Aruba</displayName>
				<displayName count="other">floríns de Aruba</displayName>
				<symbol draft="contributed">AWG</symbol>
			</currency>
			<currency type="AZN">
				<displayName>manat acerbaixano</displayName>
				<displayName count="one">manat acerbaixano</displayName>
				<displayName count="other">manats acerbaixanos</displayName>
				<symbol draft="contributed">AZN</symbol>
			</currency>
			<currency type="BAM">
				<displayName>marco convertible de Bosnia e Hercegovina</displayName>
				<displayName count="one">marco convertible de Bosnia e Hercegovina</displayName>
				<displayName count="other">marcos convertibles de Bosnia e Hercegovina</displayName>
				<symbol draft="contributed">BAM</symbol>
				<symbol alt="narrow" draft="contributed">KM</symbol>
			</currency>
			<currency type="BBD">
				<displayName>dólar de Barbados</displayName>
				<displayName count="one">dólar de Barbados</displayName>
				<displayName count="other">dólares de Barbados</displayName>
				<symbol draft="contributed">BBD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="BDT">
				<displayName>taka de Bangladesh</displayName>
				<displayName count="one">taka de Bangladesh</displayName>
				<displayName count="other">takas de Bangladesh</displayName>
				<symbol draft="contributed">BDT</symbol>
				<symbol alt="narrow" draft="contributed">৳</symbol>
			</currency>
			<currency type="BEC">
				<displayName draft="contributed">Franco belga (convertible)</displayName>
				<displayName count="one" draft="contributed">franco belga (convertible)</displayName>
				<displayName count="other" draft="contributed">francos belgas (convertibles)</displayName>
			</currency>
			<currency type="BEF">
				<displayName draft="contributed">Franco belga</displayName>
				<displayName count="one" draft="contributed">franco belga</displayName>
				<displayName count="other" draft="contributed">francos belgas</displayName>
			</currency>
			<currency type="BEL">
				<displayName draft="contributed">Franco belga (financeiro)</displayName>
				<displayName count="one" draft="contributed">franco belga (financeiro)</displayName>
				<displayName count="other" draft="contributed">francos belgas (financeiros)</displayName>
			</currency>
			<currency type="BGN">
				<displayName>lev búlgaro</displayName>
				<displayName count="one">lev búlgaro</displayName>
				<displayName count="other">leva búlgaros</displayName>
				<symbol draft="contributed">BGN</symbol>
			</currency>
			<currency type="BHD">
				<displayName>dinar de Bahrain</displayName>
				<displayName count="one">dinar de Bahrain</displayName>
				<displayName count="other">dinares de Bahrain</displayName>
				<symbol draft="contributed">BHD</symbol>
			</currency>
			<currency type="BIF">
				<displayName>franco burundiano</displayName>
				<displayName count="one">franco burundiano</displayName>
				<displayName count="other">francos burundianos</displayName>
				<symbol draft="contributed">BIF</symbol>
			</currency>
			<currency type="BMD">
				<displayName>dólar bermudano</displayName>
				<displayName count="one">dólar bermudano</displayName>
				<displayName count="other">dólares bermudanos</displayName>
				<symbol draft="contributed">BMD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="BND">
				<displayName>dólar de Brunei</displayName>
				<displayName count="one">dólar de Brunei</displayName>
				<displayName count="other">dólares de Brunei</displayName>
				<symbol draft="contributed">BND</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="BOB">
				<displayName>boliviano</displayName>
				<displayName count="one">boliviano</displayName>
				<displayName count="other">bolivianos</displayName>
				<symbol draft="contributed">BOB</symbol>
				<symbol alt="narrow" draft="contributed">Bs</symbol>
			</currency>
			<currency type="BOP">
				<displayName draft="contributed">Peso boliviano</displayName>
				<displayName count="one" draft="contributed">peso boliviano</displayName>
				<displayName count="other" draft="contributed">pesos bolivianos</displayName>
			</currency>
			<currency type="BOV">
				<displayName draft="contributed">MVDOL boliviano</displayName>
			</currency>
			<currency type="BRB">
				<displayName draft="contributed">Cruzeiro novo brasileiro (1967–1986)</displayName>
				<displayName count="one" draft="contributed">cruzeiro novo brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzeiros novos brasileiros</displayName>
			</currency>
			<currency type="BRC">
				<displayName draft="contributed">Cruzado brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzado brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzados brasileiros</displayName>
			</currency>
			<currency type="BRE">
				<displayName draft="contributed">Cruzeiro brasileiro (1990–1993)</displayName>
				<displayName count="one" draft="contributed">cruzeiro brasileiro (BRE)</displayName>
				<displayName count="other" draft="contributed">cruzeiros brasileiros (BRE)</displayName>
			</currency>
			<currency type="BRL">
				<displayName>real brasileiro</displayName>
				<displayName count="one">real brasileiro</displayName>
				<displayName count="other">reais brasileiros</displayName>
				<symbol draft="contributed">R$</symbol>
				<symbol alt="narrow" draft="contributed">R$</symbol>
			</currency>
			<currency type="BRN">
				<displayName draft="contributed">Cruzado novo brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzado novo brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzados novos brasileiros</displayName>
			</currency>
			<currency type="BRR">
				<displayName draft="contributed">Cruzeiro brasileiro</displayName>
				<displayName count="one" draft="contributed">cruzeiro brasileiro</displayName>
				<displayName count="other" draft="contributed">cruzeiros brasileiros</displayName>
			</currency>
			<currency type="BSD">
				<displayName>dólar bahamés</displayName>
				<displayName count="one">dólar bahamés</displayName>
				<displayName count="other">dólares bahameses</displayName>
				<symbol draft="contributed">BSD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="BTN">
				<displayName>ngultrum butanés</displayName>
				<displayName count="one">ngultrum butanés</displayName>
				<displayName count="other">ngultrums butaneses</displayName>
				<symbol draft="contributed">BTN</symbol>
			</currency>
			<currency type="BWP">
				<displayName>pula botswaniano</displayName>
				<displayName count="one">pula botswaniano</displayName>
				<displayName count="other">pulas botswanianos</displayName>
				<symbol draft="contributed">BWP</symbol>
				<symbol alt="narrow" draft="contributed">P</symbol>
			</currency>
			<currency type="BYN">
				<displayName>rublo belaruso</displayName>
				<displayName count="one">rublo belaruso</displayName>
				<displayName count="other">rublos belarusos</displayName>
				<symbol draft="contributed">BYN</symbol>
				<symbol alt="narrow" draft="contributed">Br</symbol>
			</currency>
			<currency type="BYR">
				<displayName>Rublo bielorruso (2000–2016)</displayName>
				<displayName count="one">rublo bielorruso (2000–2016)</displayName>
				<displayName count="other">rublos bielorrusos (2000–2016)</displayName>
				<symbol draft="contributed">BYR</symbol>
			</currency>
			<currency type="BZD">
				<displayName>dólar belizense</displayName>
				<displayName count="one">dólar belizense</displayName>
				<displayName count="other">dólares belizenses</displayName>
				<symbol draft="contributed">BZD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CAD">
				<displayName>dólar canadense</displayName>
				<displayName count="one">dólar canadense</displayName>
				<displayName count="other">dólares canadenses</displayName>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CDF">
				<displayName>franco congolés</displayName>
				<displayName count="one">franco congolés</displayName>
				<displayName count="other">francos congoleses</displayName>
				<symbol draft="contributed">CDF</symbol>
			</currency>
			<currency type="CHF">
				<displayName>franco suízo</displayName>
				<displayName count="one">franco suízo</displayName>
				<displayName count="other">francos suízos</displayName>
				<symbol draft="contributed">CHF</symbol>
			</currency>
			<currency type="CLF">
				<displayName draft="contributed">Unidades de fomento chilenas</displayName>
				<displayName count="one" draft="contributed">unidade de fomento chilena</displayName>
				<displayName count="other" draft="contributed">unidades de fomento chilenas</displayName>
			</currency>
			<currency type="CLP">
				<displayName>peso chileno</displayName>
				<displayName count="one">peso chileno</displayName>
				<displayName count="other">pesos chilenos</displayName>
				<symbol draft="contributed">CLP</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CNH">
				<displayName>iuán chinés (extracontinental)</displayName>
				<displayName count="one">iuán chinés (extracontinental)</displayName>
				<displayName count="other">iuáns chineses (extracontinentais)</displayName>
				<symbol draft="contributed">CNH</symbol>
			</currency>
			<currency type="CNY">
				<displayName>iuán chinés</displayName>
				<displayName count="one">iuán chinés</displayName>
				<displayName count="other">iuáns chineses</displayName>
				<symbol draft="contributed">CN¥</symbol>
				<symbol alt="narrow" draft="contributed">¥</symbol>
			</currency>
			<currency type="COP">
				<displayName>peso colombiano</displayName>
				<displayName count="one">peso colombiano</displayName>
				<displayName count="other">pesos colombianos</displayName>
				<symbol draft="contributed">COP</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CRC">
				<displayName>colón costarriqueño</displayName>
				<displayName count="one">colón costarriqueño</displayName>
				<displayName count="other">colóns costarriqueños</displayName>
				<symbol draft="contributed">CRC</symbol>
				<symbol alt="narrow" draft="contributed">₡</symbol>
			</currency>
			<currency type="CUC">
				<displayName>peso cubano convertible</displayName>
				<displayName count="one">peso cubano convertible</displayName>
				<displayName count="other">pesos cubanos convertibles</displayName>
				<symbol draft="contributed">CUC</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CUP">
				<displayName>peso cubano</displayName>
				<displayName count="one">peso cubano</displayName>
				<displayName count="other">pesos cubanos</displayName>
				<symbol draft="contributed">CUP</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="CVE">
				<displayName>escudo caboverdiano</displayName>
				<displayName count="one">escudo caboverdiano</displayName>
				<displayName count="other">escudos caboverdianos</displayName>
				<symbol draft="contributed">CVE</symbol>
			</currency>
			<currency type="CZK">
				<displayName>coroa checa</displayName>
				<displayName count="one">coroa checa</displayName>
				<displayName count="other">coroas checas</displayName>
				<symbol draft="contributed">CZK</symbol>
				<symbol alt="narrow" draft="contributed">Kč</symbol>
			</currency>
			<currency type="DEM">
				<displayName draft="contributed">Marco alemán</displayName>
				<displayName count="one" draft="contributed">marco alemán</displayName>
				<displayName count="other" draft="contributed">marcos alemáns</displayName>
			</currency>
			<currency type="DJF">
				<displayName>franco djibutiano</displayName>
				<displayName count="one">franco djibutiano</displayName>
				<displayName count="other">francos djibutianos</displayName>
				<symbol draft="contributed">DJF</symbol>
			</currency>
			<currency type="DKK">
				<displayName>coroa dinamarquesa</displayName>
				<displayName count="one">coroa dinamarquesa</displayName>
				<displayName count="other">coroas dinamarquesas</displayName>
				<symbol draft="contributed">DKK</symbol>
				<symbol alt="narrow" draft="contributed">kr</symbol>
			</currency>
			<currency type="DOP">
				<displayName>peso dominicano</displayName>
				<displayName count="one">peso dominicano</displayName>
				<displayName count="other">pesos dominicanos</displayName>
				<symbol draft="contributed">DOP</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="DZD">
				<displayName>dinar alxeriano</displayName>
				<displayName count="one">dinar alxeriano</displayName>
				<displayName count="other">dinares alxerianos</displayName>
				<symbol draft="contributed">DZD</symbol>
			</currency>
			<currency type="ECS">
				<displayName draft="contributed">Sucre ecuatoriano</displayName>
				<displayName count="one" draft="contributed">sucre ecuatoriano</displayName>
				<displayName count="other" draft="contributed">sucres ecuatorianos</displayName>
			</currency>
			<currency type="ECV">
				<displayName draft="contributed">Unidade de valor constante ecuatoriana</displayName>
			</currency>
			<currency type="EGP">
				<displayName>libra exipcia</displayName>
				<displayName count="one">libra exipcia</displayName>
				<displayName count="other">libras exipcias</displayName>
				<symbol draft="contributed">EGP</symbol>
				<symbol alt="narrow" draft="contributed">E£</symbol>
			</currency>
			<currency type="ERN">
				<displayName>nakfa eritreo</displayName>
				<displayName count="one">nakfa eritreo</displayName>
				<displayName count="other">nakfas eritreos</displayName>
				<symbol draft="contributed">ERN</symbol>
			</currency>
			<currency type="ESA">
				<displayName draft="contributed">Peseta española (conta A)</displayName>
			</currency>
			<currency type="ESB">
				<displayName draft="contributed">Peseta española (conta convertible)</displayName>
			</currency>
			<currency type="ESP">
				<pattern>#,##0.00 ¤</pattern>
				<displayName draft="contributed">Peseta española</displayName>
				<displayName count="one" draft="contributed">peseta</displayName>
				<displayName count="other" draft="contributed">pesetas</displayName>
				<symbol>₧</symbol>
				<decimal>,</decimal>
				<group>.</group>
			</currency>
			<currency type="ETB">
				<displayName>birr etíope</displayName>
				<displayName count="one">birr etíope</displayName>
				<displayName count="other">birres etíopes</displayName>
				<symbol draft="contributed">ETB</symbol>
			</currency>
			<currency type="EUR">
				<displayName>euro</displayName>
				<displayName count="one">euro</displayName>
				<displayName count="other">euros</displayName>
				<symbol draft="contributed">€</symbol>
				<symbol alt="narrow" draft="contributed">€</symbol>
			</currency>
			<currency type="FJD">
				<displayName>dólar fixiano</displayName>
				<displayName count="one">dólar fixiano</displayName>
				<displayName count="other">dólares fixianos</displayName>
				<symbol draft="contributed">FJD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="FKP">
				<displayName>libra das Illas Malvinas</displayName>
				<displayName count="one">libra das Illas Malvinas</displayName>
				<displayName count="other">libras das Illas Malvinas</displayName>
				<symbol draft="contributed">FKP</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="FRF">
				<displayName draft="contributed">Franco francés</displayName>
				<displayName count="one" draft="contributed">franco francés</displayName>
				<displayName count="other" draft="contributed">francos franceses</displayName>
			</currency>
			<currency type="GBP">
				<displayName>libra esterlina</displayName>
				<displayName count="one">libra esterlina</displayName>
				<displayName count="other">libras esterlinas</displayName>
				<symbol draft="contributed">£</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="GEL">
				<displayName>lari xeorxiano</displayName>
				<displayName count="one">lari xeorxiano</displayName>
				<displayName count="other">laris xeorxianos</displayName>
				<symbol draft="contributed">GEL</symbol>
				<symbol alt="narrow" draft="contributed">₾</symbol>
				<symbol alt="variant" draft="contributed">₾</symbol>
			</currency>
			<currency type="GHS">
				<displayName>cedi ghanés</displayName>
				<displayName count="one">cedi ghanés</displayName>
				<displayName count="other">cedis ghaneses</displayName>
				<symbol draft="contributed">GHS</symbol>
			</currency>
			<currency type="GIP">
				<displayName>libra xibraltareña</displayName>
				<displayName count="one">libra xibraltareña</displayName>
				<displayName count="other">libras xibraltareñas</displayName>
				<symbol draft="contributed">GIP</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="GMD">
				<displayName>dalasi gambiano</displayName>
				<displayName count="one">dalasi gambiano</displayName>
				<displayName count="other">dalasis gambianos</displayName>
				<symbol draft="contributed">GMD</symbol>
			</currency>
			<currency type="GNF">
				<displayName>franco guineano</displayName>
				<displayName count="one">franco guineano</displayName>
				<displayName count="other">francos guineanos</displayName>
				<symbol draft="contributed">GNF</symbol>
				<symbol alt="narrow" draft="contributed">FG</symbol>
			</currency>
			<currency type="GNS">
				<displayName draft="contributed">Syli guineano</displayName>
			</currency>
			<currency type="GQE">
				<displayName draft="contributed">Ekwele guineana</displayName>
			</currency>
			<currency type="GRD">
				<displayName draft="contributed">Dracma grego</displayName>
			</currency>
			<currency type="GTQ">
				<displayName>quetzal guatemalteco</displayName>
				<displayName count="one">quetzal guatemalteco</displayName>
				<displayName count="other">quetzais guatemaltecos</displayName>
				<symbol draft="contributed">GTQ</symbol>
				<symbol alt="narrow" draft="contributed">Q</symbol>
			</currency>
			<currency type="GYD">
				<displayName>dólar güianés</displayName>
				<displayName count="one">dólar güianés</displayName>
				<displayName count="other">dólares güianeses</displayName>
				<symbol draft="contributed">GYD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="HKD">
				<displayName>dólar de Hong Kong</displayName>
				<displayName count="one">dólar de Hong Kong</displayName>
				<displayName count="other">dólares de Hong Kong</displayName>
				<symbol draft="contributed">HK$</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="HNL">
				<displayName>lempira hondureño</displayName>
				<displayName count="one">lempira hondureño</displayName>
				<displayName count="other">lempiras hondureños</displayName>
				<symbol draft="contributed">HNL</symbol>
				<symbol alt="narrow" draft="contributed">L</symbol>
			</currency>
			<currency type="HRK">
				<displayName>kuna croata</displayName>
				<displayName count="one">kuna croata</displayName>
				<displayName count="other">kunas croatas</displayName>
				<symbol draft="contributed">HRK</symbol>
				<symbol alt="narrow" draft="contributed">kn</symbol>
			</currency>
			<currency type="HTG">
				<displayName>gourde haitiana</displayName>
				<displayName count="one">gourde haitiana</displayName>
				<displayName count="other">gourdes haitianas</displayName>
				<symbol draft="contributed">HTG</symbol>
			</currency>
			<currency type="HUF">
				<displayName>florín húngaro</displayName>
				<displayName count="one">florín húngaro</displayName>
				<displayName count="other">floríns húngaros</displayName>
				<symbol draft="contributed">HUF</symbol>
				<symbol alt="narrow" draft="contributed">Ft</symbol>
			</currency>
			<currency type="IDR">
				<displayName>rupia indonesia</displayName>
				<displayName count="one">rupia indonesia</displayName>
				<displayName count="other">rupias indonesias</displayName>
				<symbol draft="contributed">IDR</symbol>
				<symbol alt="narrow" draft="contributed">Rp</symbol>
			</currency>
			<currency type="IEP">
				<displayName draft="contributed">Libra irlandesa</displayName>
				<displayName count="one" draft="contributed">libra irlandesa</displayName>
				<displayName count="other" draft="contributed">libras irlandesas</displayName>
			</currency>
			<currency type="ILS">
				<displayName>novo shequel israelí</displayName>
				<displayName count="one">novo shequel israelí</displayName>
				<displayName count="other">novos shequeis israelís</displayName>
				<symbol draft="contributed">₪</symbol>
				<symbol alt="narrow" draft="contributed">₪</symbol>
			</currency>
			<currency type="INR">
				<displayName>rupia india</displayName>
				<displayName count="one">rupia india</displayName>
				<displayName count="other">rupias indias</displayName>
				<symbol draft="contributed">₹</symbol>
				<symbol alt="narrow" draft="contributed">₹</symbol>
			</currency>
			<currency type="IQD">
				<displayName>dinar iraquí</displayName>
				<displayName count="one">dinar iraquí</displayName>
				<displayName count="other">dinares iraquíes</displayName>
				<symbol draft="contributed">IQD</symbol>
			</currency>
			<currency type="IRR">
				<displayName>rial iraniano</displayName>
				<displayName count="one">rial iraniano</displayName>
				<displayName count="other">riales iranianos</displayName>
				<symbol draft="contributed">IRR</symbol>
			</currency>
			<currency type="ISK">
				<displayName>coroa islandesa</displayName>
				<displayName count="one">coroa islandesa</displayName>
				<displayName count="other">coroas islandesas</displayName>
				<symbol draft="contributed">ISK</symbol>
				<symbol alt="narrow" draft="contributed">kr</symbol>
			</currency>
			<currency type="ITL">
				<displayName draft="contributed">Lira italiana</displayName>
			</currency>
			<currency type="JMD">
				<displayName>dólar xamaicano</displayName>
				<displayName count="one">dólar xamaicano</displayName>
				<displayName count="other">dólares xamaicanos</displayName>
				<symbol draft="contributed">JMD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="JOD">
				<displayName>dinar xordano</displayName>
				<displayName count="one">dinar xordano</displayName>
				<displayName count="other">dinares xordanos</displayName>
				<symbol draft="contributed">JOD</symbol>
			</currency>
			<currency type="JPY">
				<displayName>ien xaponés</displayName>
				<displayName count="one">ien xaponés</displayName>
				<displayName count="other">iens xaponeses</displayName>
				<symbol draft="contributed">JP¥</symbol>
				<symbol alt="narrow" draft="contributed">¥</symbol>
			</currency>
			<currency type="KES">
				<displayName>xilin kenyano</displayName>
				<displayName count="one">xilin kenyano</displayName>
				<displayName count="other">xilins kenyanos</displayName>
				<symbol draft="contributed">KES</symbol>
			</currency>
			<currency type="KGS">
				<displayName>som kirguiz</displayName>
				<displayName count="one">som kirguiz</displayName>
				<displayName count="other">soms kirguiz</displayName>
				<symbol draft="contributed">KGS</symbol>
			</currency>
			<currency type="KHR">
				<displayName>riel camboxano</displayName>
				<displayName count="one">riel camboxano</displayName>
				<displayName count="other">rieis camboxanos</displayName>
				<symbol draft="contributed">KHR</symbol>
				<symbol alt="narrow" draft="contributed">៛</symbol>
			</currency>
			<currency type="KMF">
				<displayName>franco comoriano</displayName>
				<displayName count="one">franco comoriano</displayName>
				<displayName count="other">francos comorianos</displayName>
				<symbol draft="contributed">KMF</symbol>
				<symbol alt="narrow" draft="contributed">FC</symbol>
			</currency>
			<currency type="KPW">
				<displayName>won norcoreano</displayName>
				<displayName count="one">won norcoreano</displayName>
				<displayName count="other">wons norcoreanos</displayName>
				<symbol draft="contributed">KPW</symbol>
				<symbol alt="narrow" draft="contributed">₩</symbol>
			</currency>
			<currency type="KRW">
				<displayName>won surcoreano</displayName>
				<displayName count="one">won surcoreano</displayName>
				<displayName count="other">wons surcoreanos</displayName>
				<symbol draft="contributed">₩</symbol>
				<symbol alt="narrow" draft="contributed">₩</symbol>
			</currency>
			<currency type="KWD">
				<displayName>dinar kuwaití</displayName>
				<displayName count="one">dinar kuwaití</displayName>
				<displayName count="other">dinares kuwaitís</displayName>
				<symbol draft="contributed">KWD</symbol>
			</currency>
			<currency type="KYD">
				<displayName>dólar das Illas Caimán</displayName>
				<displayName count="one">dólar das Illas Caimán</displayName>
				<displayName count="other">dólares das Illas Caimán</displayName>
				<symbol draft="contributed">KYD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="KZT">
				<displayName>tenge kazako</displayName>
				<displayName count="one">tenge kazako</displayName>
				<displayName count="other">tenges kazakos</displayName>
				<symbol draft="contributed">KZT</symbol>
				<symbol alt="narrow" draft="contributed">₸</symbol>
			</currency>
			<currency type="LAK">
				<displayName>kip laosiano</displayName>
				<displayName count="one">kip laosiano</displayName>
				<displayName count="other">kips laosianos</displayName>
				<symbol draft="contributed">LAK</symbol>
				<symbol alt="narrow" draft="contributed">₭</symbol>
			</currency>
			<currency type="LBP">
				<displayName>libra libanesa</displayName>
				<displayName count="one">libra libanesa</displayName>
				<displayName count="other">libras libanesas</displayName>
				<symbol draft="contributed">LBP</symbol>
				<symbol alt="narrow" draft="contributed">L£</symbol>
			</currency>
			<currency type="LKR">
				<displayName>rupia srilankesa</displayName>
				<displayName count="one">rupia srilankesa</displayName>
				<displayName count="other">rupias srilankesas</displayName>
				<symbol draft="contributed">LKR</symbol>
				<symbol alt="narrow" draft="contributed">Rs</symbol>
			</currency>
			<currency type="LRD">
				<displayName>dólar liberiano</displayName>
				<displayName count="one">dólar liberiano</displayName>
				<displayName count="other">dólares liberianos</displayName>
				<symbol draft="contributed">LRD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="LSL">
				<displayName>Loti de Lesoto</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Litas lituana</displayName>
				<displayName count="one">litas lituana</displayName>
				<displayName count="other">litas lituanas</displayName>
				<symbol>LTL</symbol>
			</currency>
			<currency type="LUC">
				<displayName draft="contributed">Franco convertible luxemburgués</displayName>
			</currency>
			<currency type="LUF">
				<displayName draft="contributed">Franco luxemburgués</displayName>
			</currency>
			<currency type="LUL">
				<displayName draft="contributed">Franco financeiro luxemburgués</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Lats letón</displayName>
				<displayName count="one">lats letón</displayName>
				<displayName count="other">lats letóns</displayName>
				<symbol>LVL</symbol>
			</currency>
			<currency type="LYD">
				<displayName>dinar libio</displayName>
				<displayName count="one">dinar libio</displayName>
				<displayName count="other">dinares libios</displayName>
				<symbol draft="contributed">LYD</symbol>
			</currency>
			<currency type="MAD">
				<displayName>dirham marroquí</displayName>
				<displayName count="one">dirham marroquí</displayName>
				<displayName count="other">dirhams marroquís</displayName>
				<symbol draft="contributed">MAD</symbol>
			</currency>
			<currency type="MAF">
				<displayName draft="contributed">Franco marroquí</displayName>
			</currency>
			<currency type="MDL">
				<displayName>leu moldavo</displayName>
				<displayName count="one">leu moldavo</displayName>
				<displayName count="other">leus moldavos</displayName>
				<symbol draft="contributed">MDL</symbol>
			</currency>
			<currency type="MGA">
				<displayName>ariary malgaxe</displayName>
				<displayName count="one">ariary malgaxe</displayName>
				<displayName count="other">ariarys malgaxes</displayName>
				<symbol draft="contributed">MGA</symbol>
				<symbol alt="narrow" draft="contributed">Ar</symbol>
			</currency>
			<currency type="MKD">
				<displayName>dinar macedonio</displayName>
				<displayName count="one">dinar macedonio</displayName>
				<displayName count="other">dinares macedonios</displayName>
				<symbol draft="contributed">MKD</symbol>
			</currency>
			<currency type="MMK">
				<displayName>kyat birmano</displayName>
				<displayName count="one">kyat birmano</displayName>
				<displayName count="other">kyats birmanos</displayName>
				<symbol draft="contributed">MMK</symbol>
				<symbol alt="narrow" draft="contributed">K</symbol>
			</currency>
			<currency type="MNT">
				<displayName>tugrik mongol</displayName>
				<displayName count="one">tugrik mongol</displayName>
				<displayName count="other">tugriks mongois</displayName>
				<symbol draft="contributed">MNT</symbol>
				<symbol alt="narrow" draft="contributed">₮</symbol>
			</currency>
			<currency type="MOP">
				<displayName>pataca macaense</displayName>
				<displayName count="one">pataca macaense</displayName>
				<displayName count="other">patacas macaenses</displayName>
				<symbol draft="contributed">MOP</symbol>
			</currency>
			<currency type="MRO">
				<displayName>Ouguiya mauritano (1973–2017)</displayName>
				<displayName count="one">ouguiya mauritano (1973–2017)</displayName>
				<displayName count="other">ouguiyas mauritanos (1973–2017)</displayName>
				<symbol draft="contributed">MRO</symbol>
			</currency>
			<currency type="MRU">
				<displayName>ouguiya mauritano</displayName>
				<displayName count="one">ouguiya mauritano</displayName>
				<displayName count="other">ouguiyas mauritanos</displayName>
				<symbol draft="contributed">MRU</symbol>
			</currency>
			<currency type="MUR">
				<displayName>rupia mauriciana</displayName>
				<displayName count="one">rupia mauriciana</displayName>
				<displayName count="other">rupias mauricianas</displayName>
				<symbol draft="contributed">MUR</symbol>
				<symbol alt="narrow" draft="contributed">Rs</symbol>
			</currency>
			<currency type="MVR">
				<displayName>rupia maldivana</displayName>
				<displayName count="one">rupia maldivana</displayName>
				<displayName count="other">rupias maldivanas</displayName>
				<symbol draft="contributed">MVR</symbol>
			</currency>
			<currency type="MWK">
				<displayName>kwacha de Malawi</displayName>
				<displayName count="one">kwacha de Malawi</displayName>
				<displayName count="other">kwachas de Malawi</displayName>
				<symbol draft="contributed">MWK</symbol>
			</currency>
			<currency type="MXN">
				<displayName>peso mexicano</displayName>
				<displayName count="one">peso mexicano</displayName>
				<displayName count="other">pesos mexicanos</displayName>
				<symbol>$MX</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="MXP">
				<displayName draft="contributed">Peso de prata mexicano (1861–1992)</displayName>
			</currency>
			<currency type="MXV">
				<displayName draft="contributed">Unidade de inversión mexicana</displayName>
			</currency>
			<currency type="MYR">
				<displayName>ringgit malaio</displayName>
				<displayName count="one">ringgit malaio</displayName>
				<displayName count="other">ringgits malaios</displayName>
				<symbol draft="contributed">MYR</symbol>
				<symbol alt="narrow" draft="contributed">RM</symbol>
			</currency>
			<currency type="MZN">
				<displayName>metical mozambicano</displayName>
				<displayName count="one">metical mozambicano</displayName>
				<displayName count="other">meticais mozambicanos</displayName>
				<symbol draft="contributed">MZN</symbol>
			</currency>
			<currency type="NAD">
				<displayName>dólar namibio</displayName>
				<displayName count="one">dólar namibio</displayName>
				<displayName count="other">dólares namibios</displayName>
				<symbol draft="contributed">NAD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="NGN">
				<displayName>naira nixeriano</displayName>
				<displayName count="one">naira nixeriano</displayName>
				<displayName count="other">nairas nixerianos</displayName>
				<symbol draft="contributed">NGN</symbol>
				<symbol alt="narrow" draft="contributed">₦</symbol>
			</currency>
			<currency type="NIC">
				<displayName draft="contributed">Córdoba nicaragüense</displayName>
			</currency>
			<currency type="NIO">
				<displayName>córdoba nicaraguano</displayName>
				<displayName count="one">córdoba nicaraguano</displayName>
				<displayName count="other">córdobas nicaraguanos</displayName>
				<symbol draft="contributed">NIO</symbol>
				<symbol alt="narrow" draft="contributed">C$</symbol>
			</currency>
			<currency type="NLG">
				<displayName draft="contributed">Florín holandés</displayName>
			</currency>
			<currency type="NOK">
				<displayName>coroa norueguesa</displayName>
				<displayName count="one">coroa norueguesa</displayName>
				<displayName count="other">coroas norueguesas</displayName>
				<symbol draft="contributed">NOK</symbol>
				<symbol alt="narrow" draft="contributed">kr</symbol>
			</currency>
			<currency type="NPR">
				<displayName>rupia nepalesa</displayName>
				<displayName count="one">rupia nepalesa</displayName>
				<displayName count="other">rupias nepalesas</displayName>
				<symbol draft="contributed">NPR</symbol>
				<symbol alt="narrow" draft="contributed">Rs</symbol>
			</currency>
			<currency type="NZD">
				<displayName>dólar neozelandés</displayName>
				<displayName count="one">dólar neozelandés</displayName>
				<displayName count="other">dólares neozelandeses</displayName>
				<symbol draft="contributed">NZ$</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="OMR">
				<displayName>rial omaní</displayName>
				<displayName count="one">rial omaní</displayName>
				<displayName count="other">riais omanís</displayName>
				<symbol draft="contributed">OMR</symbol>
			</currency>
			<currency type="PAB">
				<displayName>balboa panameño</displayName>
				<displayName count="one">balboa panameño</displayName>
				<displayName count="other">balboas panameños</displayName>
				<symbol draft="contributed">PAB</symbol>
			</currency>
			<currency type="PEI">
				<displayName draft="contributed">Inti peruano</displayName>
			</currency>
			<currency type="PEN">
				<displayName>sol peruano</displayName>
				<displayName count="one">sol peruano</displayName>
				<displayName count="other">soles peruanos</displayName>
				<symbol draft="contributed">PEN</symbol>
			</currency>
			<currency type="PES">
				<displayName draft="contributed">Sol peruano (1863–1965)</displayName>
				<displayName count="one">sol peruano (1863–1965)</displayName>
				<displayName count="other">soles peruanos (1863–1965)</displayName>
			</currency>
			<currency type="PGK">
				<displayName>kina de Papúa-Nova Guinea</displayName>
				<displayName count="one">kina de Papúa-Nova Guinea</displayName>
				<displayName count="other">kinas de Papúa-Nova Guinea</displayName>
				<symbol draft="contributed">PGK</symbol>
			</currency>
			<currency type="PHP">
				<displayName>peso filipino</displayName>
				<displayName count="one">peso filipino</displayName>
				<displayName count="other">pesos filipinos</displayName>
				<symbol draft="contributed">PHP</symbol>
				<symbol alt="narrow" draft="contributed">₱</symbol>
			</currency>
			<currency type="PKR">
				<displayName>rupia paquistaní</displayName>
				<displayName count="one">rupia paquistaní</displayName>
				<displayName count="other">rupias paquistanís</displayName>
				<symbol draft="contributed">PKR</symbol>
				<symbol alt="narrow" draft="contributed">Rs</symbol>
			</currency>
			<currency type="PLN">
				<displayName>zloty polaco</displayName>
				<displayName count="one">zloty polaco</displayName>
				<displayName count="other">zlotys polacos</displayName>
				<symbol draft="contributed">PLN</symbol>
				<symbol alt="narrow" draft="contributed">zł</symbol>
			</currency>
			<currency type="PTE">
				<displayName draft="contributed">Escudo portugués</displayName>
				<displayName count="one" draft="contributed">escudo portugués</displayName>
				<displayName count="other" draft="contributed">escudos portugueses</displayName>
			</currency>
			<currency type="PYG">
				<displayName>guaraní paraguaio</displayName>
				<displayName count="one">guaraní paraguaio</displayName>
				<displayName count="other">guaranís paraguaios</displayName>
				<symbol draft="contributed">PYG</symbol>
				<symbol alt="narrow" draft="contributed">₲</symbol>
			</currency>
			<currency type="QAR">
				<displayName>rial qatarí</displayName>
				<displayName count="one">rial qatarí</displayName>
				<displayName count="other">riais qatarís</displayName>
				<symbol draft="contributed">QAR</symbol>
			</currency>
			<currency type="RON">
				<displayName>leu romanés</displayName>
				<displayName count="one">leu romanés</displayName>
				<displayName count="other">lei romaneses</displayName>
				<symbol draft="contributed">RON</symbol>
				<symbol alt="narrow" draft="contributed">lei</symbol>
			</currency>
			<currency type="RSD">
				<displayName>dinar serbio</displayName>
				<displayName count="one">dinar serbio</displayName>
				<displayName count="other">dinares serbios</displayName>
				<symbol draft="contributed">RSD</symbol>
			</currency>
			<currency type="RUB">
				<displayName>rublo ruso</displayName>
				<displayName count="one">rublo ruso</displayName>
				<displayName count="other">rublos rusos</displayName>
				<symbol draft="contributed">RUB</symbol>
				<symbol alt="narrow" draft="contributed">руб</symbol>
			</currency>
			<currency type="RUR">
				<displayName draft="contributed">Rublo ruso (1991–1998)</displayName>
			</currency>
			<currency type="RWF">
				<displayName>franco ruandés</displayName>
				<displayName count="one">franco ruandés</displayName>
				<displayName count="other">francos ruandeses</displayName>
				<symbol draft="contributed">RWF</symbol>
				<symbol alt="narrow" draft="contributed">RF</symbol>
			</currency>
			<currency type="SAR">
				<displayName>rial saudita</displayName>
				<displayName count="one">rial saudita</displayName>
				<displayName count="other">riais sauditas</displayName>
				<symbol draft="contributed">SAR</symbol>
			</currency>
			<currency type="SBD">
				<displayName>dólar das Illas Salomón</displayName>
				<displayName count="one">dólar das Illas Salomón</displayName>
				<displayName count="other">dólares das Illas Salomón</displayName>
				<symbol draft="contributed">SBD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="SCR">
				<displayName>rupia de Seychelles</displayName>
				<displayName count="one">rupia de Seychelles</displayName>
				<displayName count="other">rupias de Seychelles</displayName>
				<symbol draft="contributed">SCR</symbol>
			</currency>
			<currency type="SDG">
				<displayName>libra sudanesa</displayName>
				<displayName count="one">libra sudanesa</displayName>
				<displayName count="other">libras sudanesas</displayName>
				<symbol draft="contributed">SDG</symbol>
			</currency>
			<currency type="SEK">
				<displayName>coroa sueca</displayName>
				<displayName count="one">coroa sueca</displayName>
				<displayName count="other">coroas suecas</displayName>
				<symbol draft="contributed">SEK</symbol>
				<symbol alt="narrow" draft="contributed">kr</symbol>
			</currency>
			<currency type="SGD">
				<displayName>dólar de Singapur</displayName>
				<displayName count="one">dólar de Singapur</displayName>
				<displayName count="other">dólares de Singapur</displayName>
				<symbol draft="contributed">SGD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="SHP">
				<displayName>libra de Santa Helena</displayName>
				<displayName count="one">libra de Santa Helena</displayName>
				<displayName count="other">libras de Santa Helena</displayName>
				<symbol draft="contributed">SHP</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="SLL">
				<displayName>leone de Serra Leoa</displayName>
				<displayName count="one">leone de Serra Leoa</displayName>
				<displayName count="other">leones de Serra Leoa</displayName>
				<symbol draft="contributed">SLL</symbol>
			</currency>
			<currency type="SOS">
				<displayName>xilin somalí</displayName>
				<displayName count="one">xilin somalí</displayName>
				<displayName count="other">xilins somalís</displayName>
				<symbol draft="contributed">SOS</symbol>
			</currency>
			<currency type="SRD">
				<displayName>dólar surinamés</displayName>
				<displayName count="one">dólar surinamés</displayName>
				<displayName count="other">dólares surinamés</displayName>
				<symbol draft="contributed">SRD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="SSP">
				<displayName>libra sursudanesa</displayName>
				<displayName count="one">libra sursudanesa</displayName>
				<displayName count="other">libras sursudanesa</displayName>
				<symbol draft="contributed">SSP</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="STD">
				<displayName>Dobra de São Tomé e Príncipe (1977–2017)</displayName>
				<displayName count="one">dobra de São Tomé e Príncipe (1977–2017)</displayName>
				<displayName count="other">dobras de São Tomé e Príncipe (1977–2017)</displayName>
				<symbol draft="contributed">STD</symbol>
			</currency>
			<currency type="STN">
				<displayName>dobra de San Tomé e Príncipe</displayName>
				<displayName count="one">dobra de San Tomé e Príncipe</displayName>
				<displayName count="other">dobras de San Tomé e Príncipe</displayName>
				<symbol draft="contributed">STN</symbol>
				<symbol alt="narrow" draft="contributed">Db</symbol>
			</currency>
			<currency type="SUR">
				<displayName draft="contributed">Rublo soviético</displayName>
				<displayName count="one" draft="contributed">rublo soviético</displayName>
				<displayName count="other" draft="contributed">rublos soviéticos</displayName>
			</currency>
			<currency type="SVC">
				<displayName draft="contributed">Colón salvadoreño</displayName>
				<displayName count="one" draft="contributed">colón salvadoreño</displayName>
				<displayName count="other" draft="contributed">colóns salvadoreños</displayName>
			</currency>
			<currency type="SYP">
				<displayName>libra siria</displayName>
				<displayName count="one">libra siria</displayName>
				<displayName count="other">libras sirias</displayName>
				<symbol draft="contributed">SYP</symbol>
				<symbol alt="narrow" draft="contributed">£</symbol>
			</currency>
			<currency type="SZL">
				<displayName>lilangeni de Swazilandia</displayName>
				<displayName count="one">lilangeni de Swazilandia</displayName>
				<displayName count="other">lilangenis de Eswatini</displayName>
				<symbol draft="contributed">SZL</symbol>
			</currency>
			<currency type="THB">
				<displayName>baht tailandés</displayName>
				<displayName count="one">baht tailandés</displayName>
				<displayName count="other">bahts tailandeses</displayName>
				<symbol>฿</symbol>
				<symbol alt="narrow" draft="contributed">฿</symbol>
			</currency>
			<currency type="TJS">
				<displayName>somoni taxiquistano</displayName>
				<displayName count="one">somoni taxiquistano</displayName>
				<displayName count="other">somonis taxiquistanos</displayName>
				<symbol draft="contributed">TJS</symbol>
			</currency>
			<currency type="TMT">
				<displayName>manat turkmeno</displayName>
				<displayName count="one">manat turkmeno</displayName>
				<displayName count="other">manats turkmenos</displayName>
				<symbol draft="contributed">TMT</symbol>
			</currency>
			<currency type="TND">
				<displayName>dinar tunisiano</displayName>
				<displayName count="one">dinar tunisiano</displayName>
				<displayName count="other">dinares tunisianos</displayName>
				<symbol draft="contributed">TND</symbol>
			</currency>
			<currency type="TOP">
				<displayName>paʻanga tongano</displayName>
				<displayName count="one">paʻanga tongano</displayName>
				<displayName count="other">pa’angas tonganos</displayName>
				<symbol draft="contributed">TOP</symbol>
				<symbol alt="narrow" draft="contributed">T$</symbol>
			</currency>
			<currency type="TRY">
				<displayName>lira turca</displayName>
				<displayName count="one">lira turca</displayName>
				<displayName count="other">liras turcas</displayName>
				<symbol draft="contributed">TRY</symbol>
				<symbol alt="narrow" draft="contributed">₺</symbol>
				<symbol alt="variant" draft="contributed">TL</symbol>
			</currency>
			<currency type="TTD">
				<displayName>dólar trinitense</displayName>
				<displayName count="one">dólar trinitense</displayName>
				<displayName count="other">dólares trinitenses</displayName>
				<symbol draft="contributed">TTD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="TWD">
				<displayName>novo dólar taiwanés</displayName>
				<displayName count="one">novo dólar taiwanés</displayName>
				<displayName count="other">novos dólares taiwaneses</displayName>
				<symbol draft="contributed">NT$</symbol>
				<symbol alt="narrow" draft="contributed">NT$</symbol>
			</currency>
			<currency type="TZS">
				<displayName>xilin tanzano</displayName>
				<displayName count="one">xilin tanzano</displayName>
				<displayName count="other">xilins tanzanos</displayName>
				<symbol draft="contributed">TZS</symbol>
			</currency>
			<currency type="UAH">
				<displayName>hrivna ucraína</displayName>
				<displayName count="one">hrivna ucraína</displayName>
				<displayName count="other">hrivnas ucraínas</displayName>
				<symbol draft="contributed">UAH</symbol>
				<symbol alt="narrow" draft="contributed">₴</symbol>
			</currency>
			<currency type="UGX">
				<displayName>xilin ugandés</displayName>
				<displayName count="one">xilin ugandés</displayName>
				<displayName count="other">xilins ugandeses</displayName>
				<symbol draft="contributed">UGX</symbol>
			</currency>
			<currency type="USD">
				<displayName>dólar estadounidense</displayName>
				<displayName count="one">dólar estadounidense</displayName>
				<displayName count="other">dólares estadounidenses</displayName>
				<symbol>$</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="UYI">
				<displayName draft="contributed">Peso en unidades indexadas uruguaio</displayName>
			</currency>
			<currency type="UYP">
				<displayName draft="contributed">Peso uruguaio (1975–1993)</displayName>
			</currency>
			<currency type="UYU">
				<displayName>peso uruguaio</displayName>
				<displayName count="one">peso uruguaio</displayName>
				<displayName count="other">pesos uruguaios</displayName>
				<symbol draft="contributed">UYU</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="UZS">
				<displayName>som uzbeko</displayName>
				<displayName count="one">som uzbeko</displayName>
				<displayName count="other">soms uzbekos</displayName>
				<symbol draft="contributed">UZS</symbol>
			</currency>
			<currency type="VEB">
				<displayName draft="contributed">Bolívar venezolano (1871–2008)</displayName>
				<displayName count="one" draft="contributed">bolívar venezolano (1871–2008)</displayName>
				<displayName count="other" draft="contributed">bolívares venezolanos (1871–2008)</displayName>
			</currency>
			<currency type="VEF">
				<displayName>Bolívar venezolano (2008–2018)</displayName>
				<displayName count="one">bolívar venezolano (2008–2018)</displayName>
				<displayName count="other">bolívares venezolanos (2008–2018)</displayName>
				<symbol draft="contributed">VEF</symbol>
				<symbol alt="narrow" draft="contributed">Bs</symbol>
			</currency>
			<currency type="VES">
				<displayName>bolívar venezolano</displayName>
				<displayName count="one">bolívar venezolano</displayName>
				<displayName count="other">bolívares venezolanos</displayName>
				<symbol draft="contributed">VES</symbol>
			</currency>
			<currency type="VND">
				<displayName>dong vietnamita</displayName>
				<displayName count="one">dong vietnamita</displayName>
				<displayName count="other">dongs vietnamitas</displayName>
				<symbol draft="contributed">₫</symbol>
				<symbol alt="narrow" draft="contributed">₫</symbol>
			</currency>
			<currency type="VUV">
				<displayName>vatu vanuatiano</displayName>
				<displayName count="one">vatu vanuatiano</displayName>
				<displayName count="other">vatus vanuatianos</displayName>
				<symbol draft="contributed">VUV</symbol>
			</currency>
			<currency type="WST">
				<displayName>tala samoano</displayName>
				<displayName count="one">tala samoano</displayName>
				<displayName count="other">talas samoanos</displayName>
				<symbol draft="contributed">WST</symbol>
			</currency>
			<currency type="XAF">
				<displayName>franco CFA (BEAC)</displayName>
				<displayName count="one">franco CFA (BEAC)</displayName>
				<displayName count="other">francos CFA (BEAC)</displayName>
				<symbol draft="contributed">FCFA</symbol>
			</currency>
			<currency type="XAG">
				<displayName draft="contributed">Prata</displayName>
			</currency>
			<currency type="XAU">
				<displayName draft="contributed">Ouro</displayName>
			</currency>
			<currency type="XCD">
				<displayName>dólar do Caribe Oriental</displayName>
				<displayName count="one">dólar do Caribe Oriental</displayName>
				<displayName count="other">dólares do Caribe Oriental</displayName>
				<symbol draft="contributed">XCD</symbol>
				<symbol alt="narrow" draft="contributed">$</symbol>
			</currency>
			<currency type="XOF">
				<displayName>franco CFA (BCEAO)</displayName>
				<displayName count="one">franco CFA (BCEAO)</displayName>
				<displayName count="other">francos CFA (BCEAO)</displayName>
				<symbol draft="contributed">F CFA</symbol>
			</currency>
			<currency type="XPD">
				<displayName draft="contributed">Paladio</displayName>
			</currency>
			<currency type="XPF">
				<displayName>franco CFP</displayName>
				<displayName count="one">franco CFP</displayName>
				<displayName count="other">francos CFP</displayName>
				<symbol draft="contributed">CFPF</symbol>
			</currency>
			<currency type="XPT">
				<displayName draft="contributed">Platino</displayName>
			</currency>
			<currency type="XXX">
				<displayName>moeda descoñecida</displayName>
				<displayName count="one">(moeda descoñecida)</displayName>
				<displayName count="other">(moedas descoñecidas)</displayName>
			</currency>
			<currency type="YER">
				<displayName>rial iemení</displayName>
				<displayName count="one">rial iemení</displayName>
				<displayName count="other">riais iemenís</displayName>
				<symbol draft="contributed">YER</symbol>
			</currency>
			<currency type="ZAR">
				<displayName>rand surafricano</displayName>
				<displayName count="one">rand surafricano</displayName>
				<displayName count="other">rands surafricanos</displayName>
				<symbol draft="contributed">ZAR</symbol>
				<symbol alt="narrow" draft="contributed">R</symbol>
			</currency>
			<currency type="ZMK">
				<displayName>Kwacha zambiano (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>kwacha zambiano</displayName>
				<displayName count="one">kwacha zambiano</displayName>
				<displayName count="other">kwachas zambianos</displayName>
				<symbol draft="contributed">ZMW</symbol>
				<symbol alt="narrow" draft="contributed">ZK</symbol>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="approximately">~{0}</pattern>
			<pattern type="atLeast">≥{0}</pattern>
			<pattern type="atMost">≤{0}</pattern>
			<pattern type="range">{0}–{1}</pattern>
		</miscPatterns>
		<minimalPairs>
			<pluralMinimalPairs count="one">{0} día</pluralMinimalPairs>
			<pluralMinimalPairs count="other">{0} días</pluralMinimalPairs>
			<ordinalMinimalPairs ordinal="other">Colle a {0}.ª curva á dereita.</ordinalMinimalPairs>
		</minimalPairs>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="10p-1">
				<unitPrefixPattern>deci{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-2">
				<unitPrefixPattern>centi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-3">
				<unitPrefixPattern>mili{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-6">
				<unitPrefixPattern>micro{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-9">
				<unitPrefixPattern>nano{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-12">
				<unitPrefixPattern>pico{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-15">
				<unitPrefixPattern>fento{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-18">
				<unitPrefixPattern>ato{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-21">
				<unitPrefixPattern>zepto{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-24">
				<unitPrefixPattern>iocto{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p1">
				<unitPrefixPattern>deca{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p2">
				<unitPrefixPattern>hecto{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p3">
				<unitPrefixPattern>quilo{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p6">
				<unitPrefixPattern>mega{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p9">
				<unitPrefixPattern>xiga{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p12">
				<unitPrefixPattern>tera{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p15">
				<unitPrefixPattern>peta{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p18">
				<unitPrefixPattern>exa{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p21">
				<unitPrefixPattern>zeta{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p24">
				<unitPrefixPattern>iota{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p1">
				<unitPrefixPattern>quibi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p2">
				<unitPrefixPattern>mebi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p3">
				<unitPrefixPattern>xibi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p4">
				<unitPrefixPattern>tebi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p5">
				<unitPrefixPattern>pebi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p6">
				<unitPrefixPattern>exbi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p7">
				<unitPrefixPattern>zebi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="1024p8">
				<unitPrefixPattern>yobi{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="per">
				<compoundUnitPattern>{0} por {1}</compoundUnitPattern>
			</compoundUnit>
			<compoundUnit type="power2">
				<compoundUnitPattern1>{0} cadrado</compoundUnitPattern1>
				<compoundUnitPattern1 count="one">{0} cadrado</compoundUnitPattern1>
				<compoundUnitPattern1 count="other">{0} cadrados</compoundUnitPattern1>
			</compoundUnit>
			<compoundUnit type="power3">
				<compoundUnitPattern1>{0} cúbico</compoundUnitPattern1>
				<compoundUnitPattern1 count="one">{0} cúbico</compoundUnitPattern1>
				<compoundUnitPattern1 count="other">{0} cúbicos</compoundUnitPattern1>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<displayName>forzas G</displayName>
				<unitPattern count="one">{0} forza G</unitPattern>
				<unitPattern count="other">{0} forzas G</unitPattern>
			</unit>
			<unit type="acceleration-meter-per-square-second">
				<displayName>metros por segundo cadrado</displayName>
				<unitPattern count="one">{0} metro por segundo cadrado</unitPattern>
				<unitPattern count="other">{0} metros por segundo cadrado</unitPattern>
			</unit>
			<unit type="angle-revolution">
				<displayName>revolucións</displayName>
				<unitPattern count="one">{0} revolución</unitPattern>
				<unitPattern count="other">{0} revolucións</unitPattern>
			</unit>
			<unit type="angle-radian">
				<displayName>radiáns</displayName>
				<unitPattern count="one">{0} radián</unitPattern>
				<unitPattern count="other">{0} radiáns</unitPattern>
			</unit>
			<unit type="angle-degree">
				<displayName>graos</displayName>
				<unitPattern count="one">{0} grao</unitPattern>
				<unitPattern count="other">{0} graos</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<displayName>minutos de arco</displayName>
				<unitPattern count="one">{0} minuto de arco</unitPattern>
				<unitPattern count="other">{0} minutos de arco</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<displayName>segundos de arco</displayName>
				<unitPattern count="one">{0} segundo de arco</unitPattern>
				<unitPattern count="other">{0} segundos de arco</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<displayName>quilómetros cadrados</displayName>
				<unitPattern count="one">{0} quilómetro cadrado</unitPattern>
				<unitPattern count="other">{0} quilómetros cadrados</unitPattern>
				<perUnitPattern>{0} por quilómetro cadrado</perUnitPattern>
			</unit>
			<unit type="area-hectare">
				<displayName>hectáreas</displayName>
				<unitPattern count="one">{0} hectárea</unitPattern>
				<unitPattern count="other">{0} hectáreas</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<displayName>metros cadrados</displayName>
				<unitPattern count="one">{0} metro cadrado</unitPattern>
				<unitPattern count="other">{0} metros cadrados</unitPattern>
				<perUnitPattern>{0} por metro cadrado</perUnitPattern>
			</unit>
			<unit type="area-square-centimeter">
				<displayName>centímetros cadrados</displayName>
				<unitPattern count="one">{0} centímetro cadrado</unitPattern>
				<unitPattern count="other">{0} centímetros cadrados</unitPattern>
				<perUnitPattern>{0} por centímetro cadrado</perUnitPattern>
			</unit>
			<unit type="area-square-mile">
				<displayName>millas cadradas</displayName>
				<unitPattern count="one">{0} milla cadrada</unitPattern>
				<unitPattern count="other">{0} millas cadradas</unitPattern>
				<perUnitPattern>{0} por milla cadrada</perUnitPattern>
			</unit>
			<unit type="area-acre">
				<displayName>acres</displayName>
				<unitPattern count="one">{0} acre</unitPattern>
				<unitPattern count="other">{0} acres</unitPattern>
			</unit>
			<unit type="area-square-yard">
				<displayName>iardas cadradas</displayName>
				<unitPattern count="one">{0} iarda cadrada</unitPattern>
				<unitPattern count="other">{0} iardas cadradas</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<displayName>pés cadrados</displayName>
				<unitPattern count="one">{0} pé cadrado</unitPattern>
				<unitPattern count="other">{0} pés cadrados</unitPattern>
			</unit>
			<unit type="area-square-inch">
				<displayName>polgadas cadradas</displayName>
				<unitPattern count="one">{0} polgada cadrada</unitPattern>
				<unitPattern count="other">{0} polgadas cadradas</unitPattern>
				<perUnitPattern>{0} por polgada cadrada</perUnitPattern>
			</unit>
			<unit type="area-dunam">
				<displayName>dunams</displayName>
				<unitPattern count="one">{0} dunam</unitPattern>
				<unitPattern count="other">{0} dunams</unitPattern>
			</unit>
			<unit type="concentr-karat">
				<displayName>quilates</displayName>
				<unitPattern count="one">{0} quilate</unitPattern>
				<unitPattern count="other">{0} quilates</unitPattern>
			</unit>
			<unit type="concentr-milligram-ofglucose-per-deciliter">
				<displayName>miligramos por decilitro</displayName>
				<unitPattern count="one">{0} miligramo por decilitro</unitPattern>
				<unitPattern count="other">{0} miligramos por decilitro</unitPattern>
			</unit>
			<unit type="concentr-millimole-per-liter">
				<displayName>milimoles por litro</displayName>
				<unitPattern count="one">{0} milimol por litro</unitPattern>
				<unitPattern count="other">{0} milimoles por litro</unitPattern>
			</unit>
			<unit type="concentr-permillion">
				<displayName>partes por millón</displayName>
				<unitPattern count="one">{0} parte por millón</unitPattern>
				<unitPattern count="other">{0} partes por millón</unitPattern>
			</unit>
			<unit type="concentr-percent">
				<displayName>tanto por cento</displayName>
				<unitPattern count="one">{0} %</unitPattern>
				<unitPattern count="other">{0} %</unitPattern>
			</unit>
			<unit type="concentr-permille">
				<displayName>tanto por mil</displayName>
				<unitPattern count="one">{0} ‰</unitPattern>
				<unitPattern count="other">{0} ‰</unitPattern>
			</unit>
			<unit type="concentr-permyriad">
				<displayName>tanto por dez mil</displayName>
				<unitPattern count="one">{0} ‱</unitPattern>
				<unitPattern count="other">{0} ‱</unitPattern>
			</unit>
			<unit type="concentr-mole">
				<displayName>moles</displayName>
				<unitPattern count="one">{0} mol</unitPattern>
				<unitPattern count="other">{0} moles</unitPattern>
			</unit>
			<unit type="consumption-liter-per-kilometer">
				<displayName>litros por quilómetro</displayName>
				<unitPattern count="one">{0} litro por quilómetro</unitPattern>
				<unitPattern count="other">{0} litros por quilómetro</unitPattern>
			</unit>
			<unit type="consumption-liter-per-100-kilometer">
				<displayName>litros por 100 quilómetros</displayName>
				<unitPattern count="one">{0} litro por 100 quilómetros</unitPattern>
				<unitPattern count="other">{0} litros por 100 quilómetros</unitPattern>
			</unit>
			<unit type="consumption-mile-per-gallon">
				<displayName>millas por galón estadounidense</displayName>
				<unitPattern count="one">{0} milla por galón estadounidense</unitPattern>
				<unitPattern count="other">{0} millas por galón estadounidense</unitPattern>
			</unit>
			<unit type="consumption-mile-per-gallon-imperial">
				<displayName>millas por galón imperial</displayName>
				<unitPattern count="one">{0} milla por galón imperial</unitPattern>
				<unitPattern count="other">{0} millas por galón imperial</unitPattern>
			</unit>
			<unit type="digital-petabyte">
				<displayName>petabytes</displayName>
				<unitPattern count="one">{0} petabyte</unitPattern>
				<unitPattern count="other">{0} petabytes</unitPattern>
			</unit>
			<unit type="digital-terabyte">
				<displayName>terabytes</displayName>
				<unitPattern count="one">{0} terabyte</unitPattern>
				<unitPattern count="other">{0} terabytes</unitPattern>
			</unit>
			<unit type="digital-terabit">
				<displayName>terabits</displayName>
				<unitPattern count="one">{0} terabit</unitPattern>
				<unitPattern count="other">{0} terabits</unitPattern>
			</unit>
			<unit type="digital-gigabyte">
				<displayName>xigabytes</displayName>
				<unitPattern count="one">{0} xigabyte</unitPattern>
				<unitPattern count="other">{0} xigabytes</unitPattern>
			</unit>
			<unit type="digital-gigabit">
				<displayName>xigabits</displayName>
				<unitPattern count="one">{0} xigabit</unitPattern>
				<unitPattern count="other">{0} xigabits</unitPattern>
			</unit>
			<unit type="digital-megabyte">
				<displayName>megabytes</displayName>
				<unitPattern count="one">{0} megabyte</unitPattern>
				<unitPattern count="other">{0} megabytes</unitPattern>
			</unit>
			<unit type="digital-megabit">
				<displayName>megabits</displayName>
				<unitPattern count="one">{0} megabit</unitPattern>
				<unitPattern count="other">{0} megabits</unitPattern>
			</unit>
			<unit type="digital-kilobyte">
				<displayName>kilobytes</displayName>
				<unitPattern count="one">{0} kilobyte</unitPattern>
				<unitPattern count="other">{0} kilobytes</unitPattern>
			</unit>
			<unit type="digital-kilobit">
				<displayName>kilobits</displayName>
				<unitPattern count="one">{0} kilobit</unitPattern>
				<unitPattern count="other">{0} kilobits</unitPattern>
			</unit>
			<unit type="digital-byte">
				<displayName>bytes</displayName>
				<unitPattern count="one">{0} byte</unitPattern>
				<unitPattern count="other">{0} bytes</unitPattern>
			</unit>
			<unit type="digital-bit">
				<displayName>bits</displayName>
				<unitPattern count="one">{0} bit</unitPattern>
				<unitPattern count="other">{0} bits</unitPattern>
			</unit>
			<unit type="duration-century">
				<displayName>séculos</displayName>
				<unitPattern count="one">{0} século</unitPattern>
				<unitPattern count="other">{0} séculos</unitPattern>
			</unit>
			<unit type="duration-decade">
				<displayName>décadas</displayName>
				<unitPattern count="one">{0} década</unitPattern>
				<unitPattern count="other">{0} décadas</unitPattern>
			</unit>
			<unit type="duration-year">
				<displayName>anos</displayName>
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
				<perUnitPattern>{0} por ano</perUnitPattern>
			</unit>
			<unit type="duration-month">
				<displayName>meses</displayName>
				<unitPattern count="one">{0} mes</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
				<perUnitPattern>{0} por mes</perUnitPattern>
			</unit>
			<unit type="duration-week">
				<displayName>semanas</displayName>
				<unitPattern count="one">{0} semana</unitPattern>
				<unitPattern count="other">{0} semanas</unitPattern>
				<perUnitPattern>{0} por semana</perUnitPattern>
			</unit>
			<unit type="duration-day">
				<displayName>días</displayName>
				<unitPattern count="one">{0} día</unitPattern>
				<unitPattern count="other">{0} días</unitPattern>
				<perUnitPattern>{0} por día</perUnitPattern>
			</unit>
			<unit type="duration-hour">
				<displayName>horas</displayName>
				<unitPattern count="one">{0} hora</unitPattern>
				<unitPattern count="other">{0} horas</unitPattern>
				<perUnitPattern>{0} por hora</perUnitPattern>
			</unit>
			<unit type="duration-minute">
				<displayName>minutos</displayName>
				<unitPattern count="one">{0} minuto</unitPattern>
				<unitPattern count="other">{0} minutos</unitPattern>
				<perUnitPattern>{0} por minuto</perUnitPattern>
			</unit>
			<unit type="duration-second">
				<displayName>segundos</displayName>
				<unitPattern count="one">{0} segundo</unitPattern>
				<unitPattern count="other">{0} segundos</unitPattern>
				<perUnitPattern>{0} por segundo</perUnitPattern>
			</unit>
			<unit type="duration-millisecond">
				<displayName>milisegundos</displayName>
				<unitPattern count="one">{0} milisegundo</unitPattern>
				<unitPattern count="other">{0} milisegundos</unitPattern>
			</unit>
			<unit type="duration-microsecond">
				<displayName>microsegundos</displayName>
				<unitPattern count="one">{0} microsegundo</unitPattern>
				<unitPattern count="other">{0} microsegundos</unitPattern>
			</unit>
			<unit type="duration-nanosecond">
				<displayName>nanosegundos</displayName>
				<unitPattern count="one">{0} nanosegundo</unitPattern>
				<unitPattern count="other">{0} nanosegundos</unitPattern>
			</unit>
			<unit type="electric-ampere">
				<displayName>amperes</displayName>
				<unitPattern count="one">{0} ampere</unitPattern>
				<unitPattern count="other">{0} amperes</unitPattern>
			</unit>
			<unit type="electric-milliampere">
				<displayName>miliamperes</displayName>
				<unitPattern count="one">{0} miliampere</unitPattern>
				<unitPattern count="other">{0} miliamperes</unitPattern>
			</unit>
			<unit type="electric-ohm">
				<displayName>ohms</displayName>
				<unitPattern count="one">{0} ohm</unitPattern>
				<unitPattern count="other">{0} ohms</unitPattern>
			</unit>
			<unit type="electric-volt">
				<displayName>volts</displayName>
				<unitPattern count="one">{0} volt</unitPattern>
				<unitPattern count="other">{0} volts</unitPattern>
			</unit>
			<unit type="energy-kilocalorie">
				<displayName>quilocalorías</displayName>
				<unitPattern count="one">{0} quilocaloría</unitPattern>
				<unitPattern count="other">{0} quilocalorías</unitPattern>
			</unit>
			<unit type="energy-calorie">
				<displayName>calorías</displayName>
				<unitPattern count="one">{0} caloría</unitPattern>
				<unitPattern count="other">{0} calorías</unitPattern>
			</unit>
			<unit type="energy-foodcalorie">
				<displayName>quilocalorías</displayName>
				<unitPattern count="one">{0} quilocaloría</unitPattern>
				<unitPattern count="other">{0} quilocalorías</unitPattern>
			</unit>
			<unit type="energy-kilojoule">
				<displayName>quilojoules</displayName>
				<unitPattern count="one">{0} quilojoule</unitPattern>
				<unitPattern count="other">{0} quilojoules</unitPattern>
			</unit>
			<unit type="energy-joule">
				<displayName>joules</displayName>
				<unitPattern count="one">{0} joule</unitPattern>
				<unitPattern count="other">{0} joules</unitPattern>
			</unit>
			<unit type="energy-kilowatt-hour">
				<displayName>quilowatts hora</displayName>
				<unitPattern count="one">{0} quilowatt hora</unitPattern>
				<unitPattern count="other">{0} quilowatts hora</unitPattern>
			</unit>
			<unit type="energy-electronvolt">
				<displayName>electronvolts</displayName>
				<unitPattern count="one">{0} electronvolt</unitPattern>
				<unitPattern count="other">{0} electronvolts</unitPattern>
			</unit>
			<unit type="energy-british-thermal-unit">
				<displayName>unidades térmicas británicas</displayName>
				<unitPattern count="one">{0} unidade térmica británica</unitPattern>
				<unitPattern count="other">{0} unidades térmicas británicas</unitPattern>
			</unit>
			<unit type="energy-therm-us">
				<displayName>therms estadounidenses</displayName>
				<unitPattern count="one">{0} therm estadounidense</unitPattern>
				<unitPattern count="other">{0} therms estadounidenses</unitPattern>
			</unit>
			<unit type="force-pound-force">
				<displayName>libras de forza</displayName>
				<unitPattern count="one">{0} libra de forza</unitPattern>
				<unitPattern count="other">{0} libras de forza</unitPattern>
			</unit>
			<unit type="force-newton">
				<displayName>newtons</displayName>
				<unitPattern count="one">{0} newton</unitPattern>
				<unitPattern count="other">{0} newtons</unitPattern>
			</unit>
			<unit type="frequency-gigahertz">
				<displayName>xigahertzs</displayName>
				<unitPattern count="one">{0} xigahertz</unitPattern>
				<unitPattern count="other">{0} xigahertzs</unitPattern>
			</unit>
			<unit type="frequency-megahertz">
				<displayName>megahertzs</displayName>
				<unitPattern count="one">{0} megahertz</unitPattern>
				<unitPattern count="other">{0} megahertzs</unitPattern>
			</unit>
			<unit type="frequency-kilohertz">
				<displayName>quilohertzs</displayName>
				<unitPattern count="one">{0} quilohertz</unitPattern>
				<unitPattern count="other">{0} quilohertzs</unitPattern>
			</unit>
			<unit type="frequency-hertz">
				<displayName>hertzs</displayName>
				<unitPattern count="one">{0} hertz</unitPattern>
				<unitPattern count="other">{0} hertzs</unitPattern>
			</unit>
			<unit type="graphics-em">
				<displayName>cuadratíns</displayName>
				<unitPattern count="one">{0} cuadratín</unitPattern>
				<unitPattern count="other">{0} cuadratíns</unitPattern>
			</unit>
			<unit type="graphics-pixel">
				<displayName>píxeles</displayName>
				<unitPattern count="one">{0} píxel</unitPattern>
				<unitPattern count="other">{0} píxeles</unitPattern>
			</unit>
			<unit type="graphics-megapixel">
				<displayName>megapíxeles</displayName>
				<unitPattern count="one">{0} megapíxel</unitPattern>
				<unitPattern count="other">{0} megapíxeles</unitPattern>
			</unit>
			<unit type="graphics-pixel-per-centimeter">
				<displayName>píxeles por centímetro</displayName>
				<unitPattern count="one">{0} píxel por centímetro</unitPattern>
				<unitPattern count="other">{0} píxeles por centímetro</unitPattern>
			</unit>
			<unit type="graphics-pixel-per-inch">
				<displayName>píxeles por polgada</displayName>
				<unitPattern count="one">{0} píxel por polgada</unitPattern>
				<unitPattern count="other">{0} píxeles por polgada</unitPattern>
			</unit>
			<unit type="graphics-dot-per-centimeter">
				<displayName>puntos por centímetro</displayName>
				<unitPattern count="one">{0} punto por centímetro</unitPattern>
				<unitPattern count="other">{0} puntos por centímetro</unitPattern>
			</unit>
			<unit type="graphics-dot-per-inch">
				<displayName>puntos por polgada</displayName>
				<unitPattern count="one">{0} punto por polgada</unitPattern>
				<unitPattern count="other">{0} puntos por polgada</unitPattern>
			</unit>
			<unit type="graphics-dot">
				<displayName>puntos tipográficos</displayName>
				<unitPattern count="one">{0} punto tipográfico</unitPattern>
				<unitPattern count="other">{0} puntos tipográficos</unitPattern>
			</unit>
			<unit type="length-earth-radius">
				<displayName>raio terrestre</displayName>
				<unitPattern count="one">{0} raio terrestre</unitPattern>
				<unitPattern count="other">{0} raios terrestres</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<displayName>quilómetros</displayName>
				<unitPattern count="one">{0} quilómetro</unitPattern>
				<unitPattern count="other">{0} quilómetros</unitPattern>
				<perUnitPattern>{0} por quilómetro</perUnitPattern>
			</unit>
			<unit type="length-meter">
				<displayName>metros</displayName>
				<unitPattern count="one">{0} metro</unitPattern>
				<unitPattern count="other">{0} metros</unitPattern>
				<perUnitPattern>{0} por metro</perUnitPattern>
			</unit>
			<unit type="length-decimeter">
				<displayName>decímetros</displayName>
				<unitPattern count="one">{0} decímetro</unitPattern>
				<unitPattern count="other">{0} decímetros</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<displayName>centímetros</displayName>
				<unitPattern count="one">{0} centímetro</unitPattern>
				<unitPattern count="other">{0} centímetros</unitPattern>
				<perUnitPattern>{0} por centímetro</perUnitPattern>
			</unit>
			<unit type="length-millimeter">
				<displayName>milímetros</displayName>
				<unitPattern count="one">{0} milímetro</unitPattern>
				<unitPattern count="other">{0} milímetros</unitPattern>
			</unit>
			<unit type="length-micrometer">
				<displayName>micrómetros</displayName>
				<unitPattern count="one">{0} micrómetro</unitPattern>
				<unitPattern count="other">{0} micrómetros</unitPattern>
			</unit>
			<unit type="length-nanometer">
				<displayName>nanómetros</displayName>
				<unitPattern count="one">{0} nanómetro</unitPattern>
				<unitPattern count="other">{0} nanómetros</unitPattern>
			</unit>
			<unit type="length-picometer">
				<displayName>picómetros</displayName>
				<unitPattern count="one">{0} picómetro</unitPattern>
				<unitPattern count="other">{0} picómetros</unitPattern>
			</unit>
			<unit type="length-mile">
				<displayName>millas</displayName>
				<unitPattern count="one">{0} milla</unitPattern>
				<unitPattern count="other">{0} millas</unitPattern>
			</unit>
			<unit type="length-yard">
				<displayName>iardas</displayName>
				<unitPattern count="one">{0} iarda</unitPattern>
				<unitPattern count="other">{0} iardas</unitPattern>
			</unit>
			<unit type="length-foot">
				<displayName>pés</displayName>
				<unitPattern count="one">{0} pé</unitPattern>
				<unitPattern count="other">{0} pés</unitPattern>
				<perUnitPattern>{0} por pé</perUnitPattern>
			</unit>
			<unit type="length-inch">
				<displayName>polgadas</displayName>
				<unitPattern count="one">{0} polgada</unitPattern>
				<unitPattern count="other">{0} polgadas</unitPattern>
				<perUnitPattern>{0} por polgada</perUnitPattern>
			</unit>
			<unit type="length-parsec">
				<displayName>parsecs</displayName>
				<unitPattern count="one">{0} parsec</unitPattern>
				<unitPattern count="other">{0} parsecs</unitPattern>
			</unit>
			<unit type="length-light-year">
				<displayName>anos luz</displayName>
				<unitPattern count="one">{0} ano luz</unitPattern>
				<unitPattern count="other">{0} anos luz</unitPattern>
			</unit>
			<unit type="length-astronomical-unit">
				<displayName>unidades astronómicas</displayName>
				<unitPattern count="one">{0} unidade astronómica</unitPattern>
				<unitPattern count="other">{0} unidades astronómicas</unitPattern>
			</unit>
			<unit type="length-furlong">
				<displayName>furlongs</displayName>
				<unitPattern count="one">{0} furlong</unitPattern>
				<unitPattern count="other">{0} furlongs</unitPattern>
			</unit>
			<unit type="length-fathom">
				<displayName>brazas inglesas</displayName>
				<unitPattern count="one">{0} braza inglesa</unitPattern>
				<unitPattern count="other">{0} brazas inglesas</unitPattern>
			</unit>
			<unit type="length-nautical-mile">
				<displayName>millas náuticas</displayName>
				<unitPattern count="one">{0} milla náutica</unitPattern>
				<unitPattern count="other">{0} millas náuticas</unitPattern>
			</unit>
			<unit type="length-mile-scandinavian">
				<displayName>milla escandinava</displayName>
				<unitPattern count="one">{0} milla escandinava</unitPattern>
				<unitPattern count="other">{0} millas escandinavas</unitPattern>
			</unit>
			<unit type="length-point">
				<displayName>puntos</displayName>
				<unitPattern count="one">{0} punto</unitPattern>
				<unitPattern count="other">{0} puntos</unitPattern>
			</unit>
			<unit type="length-solar-radius">
				<displayName>raios solares</displayName>
				<unitPattern count="one">{0} raio solar</unitPattern>
				<unitPattern count="other">{0} raios solares</unitPattern>
			</unit>
			<unit type="light-lux">
				<displayName>lux</displayName>
				<unitPattern count="one">{0} lux</unitPattern>
				<unitPattern count="other">{0} lux</unitPattern>
			</unit>
			<unit type="light-candela">
				<displayName>candela</displayName>
				<unitPattern count="one">{0} candela</unitPattern>
				<unitPattern count="other">{0} candelas</unitPattern>
			</unit>
			<unit type="light-lumen">
				<displayName>lumen</displayName>
				<unitPattern count="one">{0} lumen</unitPattern>
				<unitPattern count="other">{0} lumens</unitPattern>
			</unit>
			<unit type="light-solar-luminosity">
				<displayName>luminosidades solares</displayName>
				<unitPattern count="one">{0} luminosidade solar</unitPattern>
				<unitPattern count="other">{0} luminosidades solares</unitPattern>
			</unit>
			<unit type="mass-metric-ton">
				<displayName>toneladas métricas</displayName>
				<unitPattern count="one">{0} tonelada métrica</unitPattern>
				<unitPattern count="other">{0} toneladas métricas</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<displayName>quilogramos</displayName>
				<unitPattern count="one">{0} quilogramo</unitPattern>
				<unitPattern count="other">{0} quilogramos</unitPattern>
				<perUnitPattern>{0} por quilogramo</perUnitPattern>
			</unit>
			<unit type="mass-gram">
				<displayName>gramos</displayName>
				<unitPattern count="one">{0} gramo</unitPattern>
				<unitPattern count="other">{0} gramos</unitPattern>
				<perUnitPattern>{0} por gramo</perUnitPattern>
			</unit>
			<unit type="mass-milligram">
				<displayName>miligramos</displayName>
				<unitPattern count="one">{0} miligramo</unitPattern>
				<unitPattern count="other">{0} miligramos</unitPattern>
			</unit>
			<unit type="mass-microgram">
				<displayName>microgramos</displayName>
				<unitPattern count="one">{0} microgramo</unitPattern>
				<unitPattern count="other">{0} microgramos</unitPattern>
			</unit>
			<unit type="mass-ton">
				<displayName>toneladas estadounidenses</displayName>
				<unitPattern count="one">{0} tonelada estadounidense</unitPattern>
				<unitPattern count="other">{0} toneladas estadounidenses</unitPattern>
			</unit>
			<unit type="mass-stone">
				<displayName>stones</displayName>
				<unitPattern count="one">{0} stone</unitPattern>
				<unitPattern count="other">{0} stones</unitPattern>
			</unit>
			<unit type="mass-pound">
				<displayName>libras</displayName>
				<unitPattern count="one">{0} libra</unitPattern>
				<unitPattern count="other">{0} libras</unitPattern>
				<perUnitPattern>{0} por libra</perUnitPattern>
			</unit>
			<unit type="mass-ounce">
				<displayName>onzas</displayName>
				<unitPattern count="one">{0} onza</unitPattern>
				<unitPattern count="other">{0} onzas</unitPattern>
				<perUnitPattern>{0} por onza</perUnitPattern>
			</unit>
			<unit type="mass-ounce-troy">
				<displayName>onzas troy</displayName>
				<unitPattern count="one">{0} onza troy</unitPattern>
				<unitPattern count="other">{0} onzas troy</unitPattern>
			</unit>
			<unit type="mass-carat">
				<displayName>quilates</displayName>
				<unitPattern count="one">{0} quilate</unitPattern>
				<unitPattern count="other">{0} quilates</unitPattern>
			</unit>
			<unit type="mass-dalton">
				<displayName>daltons</displayName>
				<unitPattern count="one">{0} dalton</unitPattern>
				<unitPattern count="other">{0} daltons</unitPattern>
			</unit>
			<unit type="mass-earth-mass">
				<displayName>masas da Terra</displayName>
				<unitPattern count="one">{0} masa da Terra</unitPattern>
				<unitPattern count="other">{0} masas da Terra</unitPattern>
			</unit>
			<unit type="mass-solar-mass">
				<displayName>masas solares</displayName>
				<unitPattern count="one">{0} masa solar</unitPattern>
				<unitPattern count="other">{0} masas solares</unitPattern>
			</unit>
			<unit type="mass-grain">
				<displayName>gran</displayName>
				<unitPattern count="one">{0} gran</unitPattern>
				<unitPattern count="other">{0} grans</unitPattern>
			</unit>
			<unit type="power-gigawatt">
				<displayName>xigawatts</displayName>
				<unitPattern count="one">{0} xigawatt</unitPattern>
				<unitPattern count="other">{0} xigawatts</unitPattern>
			</unit>
			<unit type="power-megawatt">
				<displayName>megawatts</displayName>
				<unitPattern count="one">{0} megawatt</unitPattern>
				<unitPattern count="other">{0} megawatts</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<displayName>quilowatts</displayName>
				<unitPattern count="one">{0} quilowatt</unitPattern>
				<unitPattern count="other">{0} quilowatts</unitPattern>
			</unit>
			<unit type="power-watt">
				<displayName>watts</displayName>
				<unitPattern count="one">{0} watt</unitPattern>
				<unitPattern count="other">{0} watts</unitPattern>
			</unit>
			<unit type="power-milliwatt">
				<displayName>miliwatts</displayName>
				<unitPattern count="one">{0} miliwatt</unitPattern>
				<unitPattern count="other">{0} miliwatts</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<displayName>cabalo de potencia</displayName>
				<unitPattern count="one">{0} cabalo de potencia</unitPattern>
				<unitPattern count="other">{0} cabalos de potencia</unitPattern>
			</unit>
			<unit type="pressure-millimeter-ofhg">
				<displayName>milímetros de mercurio</displayName>
				<unitPattern count="one">{0} milímetro de mercurio</unitPattern>
				<unitPattern count="other">{0} milímetros de mercurio</unitPattern>
			</unit>
			<unit type="pressure-pound-force-per-square-inch">
				<displayName>libras por polgada cadrada</displayName>
				<unitPattern count="one">{0} libra por polgada cadrada</unitPattern>
				<unitPattern count="other">{0} libras por polgada cadrada</unitPattern>
			</unit>
			<unit type="pressure-inch-ofhg">
				<displayName>polgadas de mercurio</displayName>
				<unitPattern count="one">{0} polgada de mercurio</unitPattern>
				<unitPattern count="other">{0} polgadas de mercurio</unitPattern>
			</unit>
			<unit type="pressure-bar">
				<displayName>bares</displayName>
				<unitPattern count="one">{0} bar</unitPattern>
				<unitPattern count="other">{0} bares</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<displayName>milibares</displayName>
				<unitPattern count="one">{0} milibar</unitPattern>
				<unitPattern count="other">{0} milibares</unitPattern>
			</unit>
			<unit type="pressure-atmosphere">
				<displayName>atmosferas</displayName>
				<unitPattern count="one">{0} atmosfera</unitPattern>
				<unitPattern count="other">{0} atmosferas</unitPattern>
			</unit>
			<unit type="pressure-pascal">
				<displayName>pascais</displayName>
				<unitPattern count="one">{0} pascal</unitPattern>
				<unitPattern count="other">{0} pascais</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<displayName>hectopascais</displayName>
				<unitPattern count="one">{0} hectopascal</unitPattern>
				<unitPattern count="other">{0} hectopascais</unitPattern>
			</unit>
			<unit type="pressure-kilopascal">
				<displayName>quilopascais</displayName>
				<unitPattern count="one">{0} quilopascal</unitPattern>
				<unitPattern count="other">{0} quilopascais</unitPattern>
			</unit>
			<unit type="pressure-megapascal">
				<displayName>megapascais</displayName>
				<unitPattern count="one">{0} megapascal</unitPattern>
				<unitPattern count="other">{0} megapascais</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<displayName>quilómetros por hora</displayName>
				<unitPattern count="one">{0} quilómetro por hora</unitPattern>
				<unitPattern count="other">{0} quilómetros por hora</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<displayName>metros por segundo</displayName>
				<unitPattern count="one">{0} metro por segundo</unitPattern>
				<unitPattern count="other">{0} metros por segundo</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<displayName>millas por hora</displayName>
				<unitPattern count="one">{0} milla por hora</unitPattern>
				<unitPattern count="other">{0} millas por hora</unitPattern>
			</unit>
			<unit type="speed-knot">
				<displayName>nós</displayName>
				<unitPattern count="one">{0} nó</unitPattern>
				<unitPattern count="other">{0} nós</unitPattern>
			</unit>
			<unit type="temperature-generic">
				<displayName>graos</displayName>
				<unitPattern count="one">{0} grao</unitPattern>
				<unitPattern count="other">{0} graos</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<displayName>graos Celsius</displayName>
				<unitPattern count="one">{0} grao Celsius</unitPattern>
				<unitPattern count="other">{0} graos Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<displayName>graos Fahrenheit</displayName>
				<unitPattern count="one">{0} grao Fahrenheit</unitPattern>
				<unitPattern count="other">{0} graos Fahrenheit</unitPattern>
			</unit>
			<unit type="temperature-kelvin">
				<displayName>kelvins</displayName>
				<unitPattern count="one">{0} kelvin</unitPattern>
				<unitPattern count="other">{0} kelvins</unitPattern>
			</unit>
			<unit type="torque-pound-force-foot">
				<displayName>libras pés</displayName>
				<unitPattern count="one">{0} libra pé</unitPattern>
				<unitPattern count="other">{0} libras pés</unitPattern>
			</unit>
			<unit type="torque-newton-meter">
				<displayName>newtons metro</displayName>
				<unitPattern count="one">{0} newton metro</unitPattern>
				<unitPattern count="other">{0} newtons metro</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<displayName>quilómetros cúbicos</displayName>
				<unitPattern count="one">{0} quilómetro cúbico</unitPattern>
				<unitPattern count="other">{0} quilómetros cúbicos</unitPattern>
			</unit>
			<unit type="volume-cubic-meter">
				<displayName>metros cúbicos</displayName>
				<unitPattern count="one">{0} metro cúbico</unitPattern>
				<unitPattern count="other">{0} metros cúbicos</unitPattern>
				<perUnitPattern>{0} por metro cúbico</perUnitPattern>
			</unit>
			<unit type="volume-cubic-centimeter">
				<displayName>centímetros cúbicos</displayName>
				<unitPattern count="one">{0} centímetro cúbico</unitPattern>
				<unitPattern count="other">{0} centímetros cúbicos</unitPattern>
				<perUnitPattern>{0} por centímetro cúbico</perUnitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<displayName>millas cúbicas</displayName>
				<unitPattern count="one">{0} milla cúbica</unitPattern>
				<unitPattern count="other">{0} millas cúbicas</unitPattern>
			</unit>
			<unit type="volume-cubic-yard">
				<displayName>iardas cúbicas</displayName>
				<unitPattern count="one">{0} iarda cúbica</unitPattern>
				<unitPattern count="other">{0} iardas cúbicas</unitPattern>
			</unit>
			<unit type="volume-cubic-foot">
				<displayName>pés cúbicos</displayName>
				<unitPattern count="one">{0} pé cúbico</unitPattern>
				<unitPattern count="other">{0} pés cúbicos</unitPattern>
			</unit>
			<unit type="volume-cubic-inch">
				<displayName>polgadas cúbicas</displayName>
				<unitPattern count="one">{0} polgada cúbica</unitPattern>
				<unitPattern count="other">{0} polgadas cúbicas</unitPattern>
			</unit>
			<unit type="volume-megaliter">
				<displayName>megalitros</displayName>
				<unitPattern count="one">{0} megalitro</unitPattern>
				<unitPattern count="other">{0} megalitros</unitPattern>
			</unit>
			<unit type="volume-hectoliter">
				<displayName>hectolitros</displayName>
				<unitPattern count="one">{0} hectolitro</unitPattern>
				<unitPattern count="other">{0} hectolitros</unitPattern>
			</unit>
			<unit type="volume-liter">
				<displayName>litros</displayName>
				<unitPattern count="one">{0} litro</unitPattern>
				<unitPattern count="other">{0} litros</unitPattern>
				<perUnitPattern>{0} por litro</perUnitPattern>
			</unit>
			<unit type="volume-deciliter">
				<displayName>decilitros</displayName>
				<unitPattern count="one">{0} decilitro</unitPattern>
				<unitPattern count="other">{0} decilitros</unitPattern>
			</unit>
			<unit type="volume-centiliter">
				<displayName>centilitros</displayName>
				<unitPattern count="one">{0} centilitro</unitPattern>
				<unitPattern count="other">{0} centilitros</unitPattern>
			</unit>
			<unit type="volume-milliliter">
				<displayName>mililitros</displayName>
				<unitPattern count="one">{0} mililitro</unitPattern>
				<unitPattern count="other">{0} mililitros</unitPattern>
			</unit>
			<unit type="volume-pint-metric">
				<displayName>pintas métricas</displayName>
				<unitPattern count="one">{0} pinta métrica</unitPattern>
				<unitPattern count="other">{0} pintas métricas</unitPattern>
			</unit>
			<unit type="volume-cup-metric">
				<displayName>cuncas métricas</displayName>
				<unitPattern count="one">{0} cunca métrica</unitPattern>
				<unitPattern count="other">{0} cuncas métricas</unitPattern>
			</unit>
			<unit type="volume-acre-foot">
				<displayName>acre-pés</displayName>
				<unitPattern count="one">{0} acre-pé</unitPattern>
				<unitPattern count="other">{0} acre-pés</unitPattern>
			</unit>
			<unit type="volume-bushel">
				<displayName>bushels</displayName>
				<unitPattern count="one">{0} bushel</unitPattern>
				<unitPattern count="other">{0} bushels</unitPattern>
			</unit>
			<unit type="volume-gallon">
				<displayName>galóns estadounidenses</displayName>
				<unitPattern count="one">{0} galón estadounidense</unitPattern>
				<unitPattern count="other">{0} galóns estadounidenses</unitPattern>
				<perUnitPattern>{0} por galón estadounidense</perUnitPattern>
			</unit>
			<unit type="volume-gallon-imperial">
				<displayName>galóns imperiais</displayName>
				<unitPattern count="one">{0} galón imperial</unitPattern>
				<unitPattern count="other">{0} galóns imperiais</unitPattern>
				<perUnitPattern>{0} por galón imperial</perUnitPattern>
			</unit>
			<unit type="volume-quart">
				<displayName>cuartos</displayName>
				<unitPattern count="one">{0} cuarto</unitPattern>
				<unitPattern count="other">{0} cuartos</unitPattern>
			</unit>
			<unit type="volume-pint">
				<displayName>pintas</displayName>
				<unitPattern count="one">{0} pinta</unitPattern>
				<unitPattern count="other">{0} pintas</unitPattern>
			</unit>
			<unit type="volume-cup">
				<displayName>cuncas</displayName>
				<unitPattern count="one">{0} cunca</unitPattern>
				<unitPattern count="other">{0} cuncas</unitPattern>
			</unit>
			<unit type="volume-fluid-ounce">
				<displayName>onzas líquidas</displayName>
				<unitPattern count="one">{0} onza líquida</unitPattern>
				<unitPattern count="other">{0} onzas líquidas</unitPattern>
			</unit>
			<unit type="volume-fluid-ounce-imperial">
				<displayName>onzas líquidas imperiais</displayName>
				<unitPattern count="one">{0} onza líquida imperial</unitPattern>
				<unitPattern count="other">{0} onzas líquidas imperiais</unitPattern>
			</unit>
			<unit type="volume-tablespoon">
				<displayName>culleradas</displayName>
				<unitPattern count="one">{0} cullerada</unitPattern>
				<unitPattern count="other">{0} culleradas</unitPattern>
			</unit>
			<unit type="volume-teaspoon">
				<displayName>culleradiñas</displayName>
				<unitPattern count="one">{0} culleradiña</unitPattern>
				<unitPattern count="other">{0} culleradiñas</unitPattern>
			</unit>
			<unit type="volume-barrel">
				<displayName>barrís</displayName>
				<unitPattern count="one">{0} barril</unitPattern>
				<unitPattern count="other">{0} barrís</unitPattern>
			</unit>
			<unit type="volume-dessert-spoon">
				<displayName>cullerada de sobremesa</displayName>
				<unitPattern count="one">{0} cullerada de sobremesa</unitPattern>
				<unitPattern count="other">{0} culleradas de sobremesa</unitPattern>
			</unit>
			<unit type="volume-dessert-spoon-imperial">
				<displayName>cullerada de sobremesa imperial</displayName>
				<unitPattern count="one">{0} cullerada de sobremesa imperial</unitPattern>
				<unitPattern count="other">{0} culleradas de sobremesa imperiais</unitPattern>
			</unit>
			<unit type="volume-drop">
				<displayName>gota</displayName>
				<unitPattern count="one">{0} gota</unitPattern>
				<unitPattern count="other">{0} gotas</unitPattern>
			</unit>
			<unit type="volume-dram">
				<displayName>dracma líquida</displayName>
				<unitPattern count="one">{0} dracma líquida</unitPattern>
				<unitPattern count="other">{0} dracmas líquidas</unitPattern>
			</unit>
			<unit type="volume-jigger">
				<displayName>medidor de cóctel</displayName>
				<unitPattern count="one">{0} medidor de cóctel</unitPattern>
				<unitPattern count="other">{0} medidores de cóctel</unitPattern>
			</unit>
			<unit type="volume-pinch">
				<displayName>chisco</displayName>
				<unitPattern count="one">{0} chisco</unitPattern>
				<unitPattern count="other">{0} chiscos</unitPattern>
			</unit>
			<unit type="volume-quart-imperial">
				<displayName>cuarto imperial</displayName>
				<unitPattern count="one">{0} cuarto imperial</unitPattern>
				<unitPattern count="other">{0} cuartos imperiais</unitPattern>
			</unit>
			<coordinateUnit>
				<displayName>punto cardinal</displayName>
				<coordinateUnitPattern type="east">{0} leste</coordinateUnitPattern>
				<coordinateUnitPattern type="north">{0} norte</coordinateUnitPattern>
				<coordinateUnitPattern type="south">{0} sur</coordinateUnitPattern>
				<coordinateUnitPattern type="west">{0} oeste</coordinateUnitPattern>
			</coordinateUnit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="10p-1">
				<unitPrefixPattern>d{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-2">
				<unitPrefixPattern>c{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-3">
				<unitPrefixPattern>m{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-6">
				<unitPrefixPattern>μ{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-9">
				<unitPrefixPattern>n{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-12">
				<unitPrefixPattern>p{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-15">
				<unitPrefixPattern>f{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-18">
				<unitPrefixPattern>a{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-21">
				<unitPrefixPattern>z{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-24">
				<unitPrefixPattern>y{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p1">
				<unitPrefixPattern>da{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p2">
				<unitPrefixPattern>h{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p3">
				<unitPrefixPattern>k{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p6">
				<unitPrefixPattern>M{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p9">
				<unitPrefixPattern>G{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p12">
				<unitPrefixPattern>T{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p15">
				<unitPrefixPattern>P{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p18">
				<unitPrefixPattern>E{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p21">
				<unitPrefixPattern>Z{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p24">
				<unitPrefixPattern>Y{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<compoundUnit type="power2">
				<compoundUnitPattern1>{0}²</compoundUnitPattern1>
			</compoundUnit>
			<compoundUnit type="power3">
				<compoundUnitPattern1>{0}³</compoundUnitPattern1>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<displayName>forzas G</displayName>
				<unitPattern count="one">{0} G</unitPattern>
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="acceleration-meter-per-square-second">
				<displayName>m/s²</displayName>
				<unitPattern count="one">{0} m/s²</unitPattern>
				<unitPattern count="other">{0} m/s²</unitPattern>
			</unit>
			<unit type="angle-revolution">
				<displayName>rev</displayName>
				<unitPattern count="one">{0} rev</unitPattern>
				<unitPattern count="other">{0} rev</unitPattern>
			</unit>
			<unit type="angle-radian">
				<displayName>radiáns</displayName>
				<unitPattern count="one">{0} rad</unitPattern>
				<unitPattern count="other">{0} rad</unitPattern>
			</unit>
			<unit type="angle-degree">
				<displayName>graos</displayName>
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<displayName>minutos</displayName>
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<displayName>segundos</displayName>
				<unitPattern count="one">{0}′′</unitPattern>
				<unitPattern count="other">{0}′′</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<displayName>km²</displayName>
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
				<perUnitPattern>{0}/km²</perUnitPattern>
			</unit>
			<unit type="area-hectare">
				<displayName>hectáreas</displayName>
				<unitPattern count="one">{0} ha</unitPattern>
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<displayName>m²</displayName>
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
				<perUnitPattern>{0}/m²</perUnitPattern>
			</unit>
			<unit type="area-square-centimeter">
				<displayName>cm²</displayName>
				<unitPattern count="one">{0} cm²</unitPattern>
				<unitPattern count="other">{0} cm²</unitPattern>
				<perUnitPattern>{0}/cm²</perUnitPattern>
			</unit>
			<unit type="area-square-mile">
				<displayName>mi²</displayName>
				<unitPattern count="one">{0} mi²</unitPattern>
				<unitPattern count="other">{0} mi²</unitPattern>
				<perUnitPattern>{0}/mi²</perUnitPattern>
			</unit>
			<unit type="area-acre">
				<displayName>acres</displayName>
				<unitPattern count="one">{0} ac</unitPattern>
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-square-yard">
				<displayName>yd²</displayName>
				<unitPattern count="one">{0} yd²</unitPattern>
				<unitPattern count="other">{0} yd²</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<displayName>ft²</displayName>
				<unitPattern count="one">{0} ft²</unitPattern>
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-inch">
				<displayName>in²</displayName>
				<unitPattern count="one">{0} in²</unitPattern>
				<unitPattern count="other">{0} in²</unitPattern>
				<perUnitPattern>{0}/in²</perUnitPattern>
			</unit>
			<unit type="area-dunam">
				<displayName>dunams</displayName>
				<unitPattern count="one">{0} dunam</unitPattern>
				<unitPattern count="other">{0} dunams</unitPattern>
			</unit>
			<unit type="concentr-karat">
				<displayName>quilates</displayName>
				<unitPattern count="one">{0} kt</unitPattern>
				<unitPattern count="other">{0} kt</unitPattern>
			</unit>
			<unit type="concentr-milligram-ofglucose-per-deciliter">
				<displayName>mg/dl</displayName>
				<unitPattern count="one">{0} mg/dl</unitPattern>
				<unitPattern count="other">{0} mg/dl</unitPattern>
			</unit>
			<unit type="concentr-millimole-per-liter">
				<displayName>mmol/l</displayName>
				<unitPattern count="one">{0} mmol/l</unitPattern>
				<unitPattern count="other">{0} mmol/l</unitPattern>
			</unit>
			<unit type="concentr-permillion">
				<displayName>ppm</displayName>
				<unitPattern count="one">{0} ppm</unitPattern>
				<unitPattern count="other">{0} ppm</unitPattern>
			</unit>
			<unit type="concentr-percent">
				<displayName>%</displayName>
				<unitPattern count="one">{0} %</unitPattern>
				<unitPattern count="other">{0} %</unitPattern>
			</unit>
			<unit type="concentr-permille">
				<displayName>‰</displayName>
				<unitPattern count="one">{0} ‰</unitPattern>
				<unitPattern count="other">{0} ‰</unitPattern>
			</unit>
			<unit type="concentr-permyriad">
				<unitPattern count="one">{0} ‱</unitPattern>
				<unitPattern count="other">{0} ‱</unitPattern>
			</unit>
			<unit type="concentr-mole">
				<unitPattern count="one">{0} mol</unitPattern>
				<unitPattern count="other">{0} mol</unitPattern>
			</unit>
			<unit type="consumption-liter-per-kilometer">
				<displayName>litros/km</displayName>
				<unitPattern count="one">{0} l/km</unitPattern>
				<unitPattern count="other">{0} l/km</unitPattern>
			</unit>
			<unit type="consumption-liter-per-100-kilometer">
				<displayName>litros/100 km</displayName>
				<unitPattern count="one">{0} l/100 km</unitPattern>
				<unitPattern count="other">{0} l/100 km</unitPattern>
			</unit>
			<unit type="consumption-mile-per-gallon">
				<displayName>millas/galón EUA</displayName>
				<unitPattern count="one">{0} mpg EUA</unitPattern>
				<unitPattern count="other">{0} mpg EUA</unitPattern>
			</unit>
			<unit type="consumption-mile-per-gallon-imperial">
				<displayName>millas/gal imp.</displayName>
				<unitPattern count="one">{0} mpg imp.</unitPattern>
				<unitPattern count="other">{0} mpg imp.</unitPattern>
			</unit>
			<unit type="digital-petabyte">
				<displayName>PB</displayName>
				<unitPattern count="one">{0} PB</unitPattern>
				<unitPattern count="other">{0} PB</unitPattern>
			</unit>
			<unit type="digital-terabyte">
				<displayName>TB</displayName>
				<unitPattern count="one">{0} TB</unitPattern>
				<unitPattern count="other">{0} TB</unitPattern>
			</unit>
			<unit type="digital-terabit">
				<displayName>Tb</displayName>
				<unitPattern count="one">{0} Tb</unitPattern>
				<unitPattern count="other">{0} Tb</unitPattern>
			</unit>
			<unit type="digital-gigabyte">
				<displayName>GB</displayName>
				<unitPattern count="one">{0} GB</unitPattern>
				<unitPattern count="other">{0} GB</unitPattern>
			</unit>
			<unit type="digital-gigabit">
				<displayName>Gb</displayName>
				<unitPattern count="one">{0} Gb</unitPattern>
				<unitPattern count="other">{0} Gb</unitPattern>
			</unit>
			<unit type="digital-megabyte">
				<displayName>MB</displayName>
				<unitPattern count="one">{0} MB</unitPattern>
				<unitPattern count="other">{0} MB</unitPattern>
			</unit>
			<unit type="digital-megabit">
				<displayName>Mb</displayName>
				<unitPattern count="one">{0} Mb</unitPattern>
				<unitPattern count="other">{0} Mb</unitPattern>
			</unit>
			<unit type="digital-kilobyte">
				<displayName>kB</displayName>
				<unitPattern count="one">{0} kB</unitPattern>
				<unitPattern count="other">{0} kB</unitPattern>
			</unit>
			<unit type="digital-kilobit">
				<displayName>kb</displayName>
				<unitPattern count="one">{0} kb</unitPattern>
				<unitPattern count="other">{0} kb</unitPattern>
			</unit>
			<unit type="digital-byte">
				<displayName>byte</displayName>
				<unitPattern count="one">{0} byte</unitPattern>
				<unitPattern count="other">{0} byte</unitPattern>
			</unit>
			<unit type="digital-bit">
				<displayName>bit</displayName>
				<unitPattern count="one">{0} b</unitPattern>
				<unitPattern count="other">{0} b</unitPattern>
			</unit>
			<unit type="duration-century">
				<displayName>séc.</displayName>
				<unitPattern count="one">{0} séc.</unitPattern>
				<unitPattern count="other">{0} séc.</unitPattern>
			</unit>
			<unit type="duration-decade">
				<displayName>déc.</displayName>
				<unitPattern count="one">{0} déc.</unitPattern>
				<unitPattern count="other">{0} déc.</unitPattern>
			</unit>
			<unit type="duration-year">
				<displayName>anos</displayName>
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
				<perUnitPattern>{0}/ano</perUnitPattern>
			</unit>
			<unit type="duration-month">
				<displayName>meses</displayName>
				<unitPattern count="one">{0} mes</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
				<perUnitPattern>{0}/mes</perUnitPattern>
			</unit>
			<unit type="duration-week">
				<displayName>sem.</displayName>
				<unitPattern count="one">{0} sem.</unitPattern>
				<unitPattern count="other">{0} sem.</unitPattern>
				<perUnitPattern>{0}/sem.</perUnitPattern>
			</unit>
			<unit type="duration-day">
				<displayName>días</displayName>
				<unitPattern count="one">{0} día</unitPattern>
				<unitPattern count="other">{0} días</unitPattern>
				<perUnitPattern>{0}/día</perUnitPattern>
			</unit>
			<unit type="duration-hour">
				<displayName>h</displayName>
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
				<perUnitPattern>{0}/h</perUnitPattern>
			</unit>
			<unit type="duration-minute">
				<displayName>min</displayName>
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
				<perUnitPattern>{0}/min</perUnitPattern>
			</unit>
			<unit type="duration-second">
				<displayName>s</displayName>
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
				<perUnitPattern>{0}/s</perUnitPattern>
			</unit>
			<unit type="duration-millisecond">
				<displayName>ms</displayName>
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-microsecond">
				<displayName>μs</displayName>
				<unitPattern count="one">{0} μs</unitPattern>
				<unitPattern count="other">{0} μs</unitPattern>
			</unit>
			<unit type="duration-nanosecond">
				<displayName>ns</displayName>
				<unitPattern count="one">{0} ns</unitPattern>
				<unitPattern count="other">{0} ns</unitPattern>
			</unit>
			<unit type="electric-ampere">
				<displayName>A</displayName>
				<unitPattern count="one">{0} A</unitPattern>
				<unitPattern count="other">{0} A</unitPattern>
			</unit>
			<unit type="electric-milliampere">
				<displayName>mA</displayName>
				<unitPattern count="one">{0} mA</unitPattern>
				<unitPattern count="other">{0} mA</unitPattern>
			</unit>
			<unit type="electric-ohm">
				<displayName>ohms</displayName>
				<unitPattern count="one">{0} Ω</unitPattern>
				<unitPattern count="other">{0} Ω</unitPattern>
			</unit>
			<unit type="electric-volt">
				<displayName>volts</displayName>
				<unitPattern count="one">{0} V</unitPattern>
				<unitPattern count="other">{0} V</unitPattern>
			</unit>
			<unit type="energy-kilocalorie">
				<displayName>kcal</displayName>
				<unitPattern count="one">{0} kcal</unitPattern>
				<unitPattern count="other">{0} kcal</unitPattern>
			</unit>
			<unit type="energy-calorie">
				<displayName>cal</displayName>
				<unitPattern count="one">{0} cal</unitPattern>
				<unitPattern count="other">{0} cal</unitPattern>
			</unit>
			<unit type="energy-foodcalorie">
				<displayName>kcal</displayName>
				<unitPattern count="one">{0} kcal</unitPattern>
				<unitPattern count="other">{0} kcal</unitPattern>
			</unit>
			<unit type="energy-kilojoule">
				<displayName>quilojoule</displayName>
				<unitPattern count="one">{0} kJ</unitPattern>
				<unitPattern count="other">{0} kJ</unitPattern>
			</unit>
			<unit type="energy-joule">
				<displayName>joules</displayName>
				<unitPattern count="one">{0} J</unitPattern>
				<unitPattern count="other">{0} J</unitPattern>
			</unit>
			<unit type="energy-kilowatt-hour">
				<unitPattern count="one">{0} kWh</unitPattern>
				<unitPattern count="other">{0} kWh</unitPattern>
			</unit>
			<unit type="energy-british-thermal-unit">
				<displayName>BTU</displayName>
				<unitPattern count="one">{0} BTU</unitPattern>
				<unitPattern count="other">{0} BTU</unitPattern>
			</unit>
			<unit type="energy-therm-us">
				<displayName>thm U.S.</displayName>
				<unitPattern count="one">{0} thm U.S.</unitPattern>
				<unitPattern count="other">{0} thm U.S.</unitPattern>
			</unit>
			<unit type="force-pound-force">
				<displayName>libra forza</displayName>
			</unit>
			<unit type="force-newton">
				<displayName>newton</displayName>
			</unit>
			<unit type="frequency-gigahertz">
				<displayName>GHz</displayName>
				<unitPattern count="one">{0} GHz</unitPattern>
				<unitPattern count="other">{0} GHz</unitPattern>
			</unit>
			<unit type="frequency-megahertz">
				<displayName>MHz</displayName>
				<unitPattern count="one">{0} MHz</unitPattern>
				<unitPattern count="other">{0} MHz</unitPattern>
			</unit>
			<unit type="frequency-kilohertz">
				<displayName>kHz</displayName>
				<unitPattern count="one">{0} kHz</unitPattern>
				<unitPattern count="other">{0} kHz</unitPattern>
			</unit>
			<unit type="frequency-hertz">
				<displayName>Hz</displayName>
				<unitPattern count="one">{0} Hz</unitPattern>
				<unitPattern count="other">{0} Hz</unitPattern>
			</unit>
			<unit type="graphics-megapixel">
				<displayName>Mpx</displayName>
				<unitPattern count="one">{0} Mpx</unitPattern>
				<unitPattern count="other">{0} Mpx</unitPattern>
			</unit>
			<unit type="graphics-pixel-per-centimeter">
				<displayName>px/cm</displayName>
				<unitPattern count="one">{0} px/cm</unitPattern>
				<unitPattern count="other">{0} px/cm</unitPattern>
			</unit>
			<unit type="graphics-pixel-per-inch">
				<displayName>px/in</displayName>
				<unitPattern count="one">{0} px/in</unitPattern>
				<unitPattern count="other">{0} px/in</unitPattern>
			</unit>
			<unit type="graphics-dot-per-centimeter">
				<displayName>ppcm</displayName>
				<unitPattern count="one">{0} ppcm</unitPattern>
				<unitPattern count="other">{0} ppcm</unitPattern>
			</unit>
			<unit type="graphics-dot-per-inch">
				<displayName>ppp</displayName>
				<unitPattern count="one">{0} ppp</unitPattern>
				<unitPattern count="other">{0} ppp</unitPattern>
			</unit>
			<unit type="graphics-dot">
				<displayName>ptos.</displayName>
				<unitPattern count="one">{0} ptos.</unitPattern>
				<unitPattern count="other">{0} ptos.</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<displayName>km</displayName>
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
				<perUnitPattern>{0}/km</perUnitPattern>
			</unit>
			<unit type="length-meter">
				<displayName>m</displayName>
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
				<perUnitPattern>{0}/m</perUnitPattern>
			</unit>
			<unit type="length-decimeter">
				<displayName>dm</displayName>
				<unitPattern count="one">{0} dm</unitPattern>
				<unitPattern count="other">{0} dm</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<displayName>cm</displayName>
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
				<perUnitPattern>{0}/cm</perUnitPattern>
			</unit>
			<unit type="length-millimeter">
				<displayName>mm</displayName>
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-micrometer">
				<displayName>μm</displayName>
				<unitPattern count="one">{0} μm</unitPattern>
				<unitPattern count="other">{0} μm</unitPattern>
			</unit>
			<unit type="length-nanometer">
				<displayName>nm</displayName>
				<unitPattern count="one">{0} nm</unitPattern>
				<unitPattern count="other">{0} nm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<displayName>pm</displayName>
				<unitPattern count="one">{0} pm</unitPattern>
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-mile">
				<displayName>millas</displayName>
				<unitPattern count="one">{0} mi</unitPattern>
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-yard">
				<displayName>iardas</displayName>
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="length-foot">
				<displayName>pés</displayName>
				<unitPattern count="one">{0} ft</unitPattern>
				<unitPattern count="other">{0} ft</unitPattern>
				<perUnitPattern>{0}/ft</perUnitPattern>
			</unit>
			<unit type="length-inch">
				<displayName>polg.</displayName>
				<unitPattern count="one">{0} in</unitPattern>
				<unitPattern count="other">{0} in</unitPattern>
				<perUnitPattern>{0}/in</perUnitPattern>
			</unit>
			<unit type="length-parsec">
				<displayName>parsecs</displayName>
				<unitPattern count="one">{0} pc</unitPattern>
				<unitPattern count="other">{0} pc</unitPattern>
			</unit>
			<unit type="length-light-year">
				<displayName>anos luz</displayName>
				<unitPattern count="one">{0} a.l.</unitPattern>
				<unitPattern count="other">{0} a.l.</unitPattern>
			</unit>
			<unit type="length-astronomical-unit">
				<displayName>ua</displayName>
				<unitPattern count="one">{0} ua</unitPattern>
				<unitPattern count="other">{0} ua</unitPattern>
			</unit>
			<unit type="length-furlong">
				<displayName>furlongs</displayName>
				<unitPattern count="one">{0} fur</unitPattern>
				<unitPattern count="other">{0} fur</unitPattern>
			</unit>
			<unit type="length-fathom">
				<displayName>brazas inglesas</displayName>
				<unitPattern count="one">{0} fth</unitPattern>
				<unitPattern count="other">{0} fth</unitPattern>
			</unit>
			<unit type="length-nautical-mile">
				<displayName>M</displayName>
				<unitPattern count="one">{0} M</unitPattern>
				<unitPattern count="other">{0} M</unitPattern>
			</unit>
			<unit type="length-mile-scandinavian">
				<displayName>mi esc.</displayName>
				<unitPattern count="one">{0} mi esc.</unitPattern>
				<unitPattern count="other">{0} mi esc.</unitPattern>
			</unit>
			<unit type="length-point">
				<displayName>pt</displayName>
				<unitPattern count="one">{0} pt</unitPattern>
				<unitPattern count="other">{0} pt</unitPattern>
			</unit>
			<unit type="length-solar-radius">
				<displayName>raios solares</displayName>
			</unit>
			<unit type="light-lux">
				<displayName>lux</displayName>
				<unitPattern count="one">{0} lx</unitPattern>
				<unitPattern count="other">{0} lx</unitPattern>
			</unit>
			<unit type="light-solar-luminosity">
				<displayName>luminosidades solares</displayName>
			</unit>
			<unit type="mass-metric-ton">
				<displayName>t</displayName>
				<unitPattern count="one">{0} t</unitPattern>
				<unitPattern count="other">{0} t</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<displayName>kg</displayName>
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
				<perUnitPattern>{0}/kg</perUnitPattern>
			</unit>
			<unit type="mass-gram">
				<displayName>gramos</displayName>
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
				<perUnitPattern>{0}/g</perUnitPattern>
			</unit>
			<unit type="mass-milligram">
				<displayName>mg</displayName>
				<unitPattern count="one">{0} mg</unitPattern>
				<unitPattern count="other">{0} mg</unitPattern>
			</unit>
			<unit type="mass-microgram">
				<displayName>μg</displayName>
				<unitPattern count="one">{0} μg</unitPattern>
				<unitPattern count="other">{0} μg</unitPattern>
			</unit>
			<unit type="mass-ton">
				<displayName>tn EUA</displayName>
				<unitPattern count="one">{0} tn EUA</unitPattern>
				<unitPattern count="other">{0} tn EUA</unitPattern>
			</unit>
			<unit type="mass-pound">
				<displayName>libras</displayName>
				<unitPattern count="one">{0} lb</unitPattern>
				<unitPattern count="other">{0} lb</unitPattern>
				<perUnitPattern>{0}/lb</perUnitPattern>
			</unit>
			<unit type="mass-ounce">
				<displayName>oz</displayName>
				<unitPattern count="one">{0} oz</unitPattern>
				<unitPattern count="other">{0} oz</unitPattern>
				<perUnitPattern>{0}/oz</perUnitPattern>
			</unit>
			<unit type="mass-ounce-troy">
				<displayName>oz t</displayName>
				<unitPattern count="one">{0} oz t</unitPattern>
				<unitPattern count="other">{0} oz t</unitPattern>
			</unit>
			<unit type="mass-carat">
				<displayName>quilates</displayName>
				<unitPattern count="one">{0} ct</unitPattern>
				<unitPattern count="other">{0} ct</unitPattern>
			</unit>
			<unit type="mass-dalton">
				<displayName>daltons</displayName>
			</unit>
			<unit type="mass-earth-mass">
				<displayName>masas da Terra</displayName>
			</unit>
			<unit type="mass-solar-mass">
				<displayName>masas solares</displayName>
			</unit>
			<unit type="mass-grain">
				<displayName>gran</displayName>
				<unitPattern count="one">{0} gran</unitPattern>
				<unitPattern count="other">{0} grans</unitPattern>
			</unit>
			<unit type="power-gigawatt">
				<displayName>GW</displayName>
				<unitPattern count="one">{0} GW</unitPattern>
				<unitPattern count="other">{0} GW</unitPattern>
			</unit>
			<unit type="power-megawatt">
				<displayName>MW</displayName>
				<unitPattern count="one">{0} MW</unitPattern>
				<unitPattern count="other">{0} MW</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<displayName>kW</displayName>
				<unitPattern count="one">{0} kW</unitPattern>
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<displayName>watts</displayName>
				<unitPattern count="one">{0} W</unitPattern>
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="power-milliwatt">
				<displayName>mW</displayName>
				<unitPattern count="one">{0} mW</unitPattern>
				<unitPattern count="other">{0} mW</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<displayName>hp</displayName>
				<unitPattern count="one">{0} hp</unitPattern>
				<unitPattern count="other">{0} hp</unitPattern>
			</unit>
			<unit type="pressure-millimeter-ofhg">
				<displayName>mmHg</displayName>
				<unitPattern count="one">{0} mmHg</unitPattern>
				<unitPattern count="other">{0} mmHg</unitPattern>
			</unit>
			<unit type="pressure-pound-force-per-square-inch">
				<displayName>psi</displayName>
				<unitPattern count="one">{0} psi</unitPattern>
				<unitPattern count="other">{0} psi</unitPattern>
			</unit>
			<unit type="pressure-inch-ofhg">
				<displayName>inHg</displayName>
				<unitPattern count="one">{0} inHg</unitPattern>
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-bar">
				<displayName>bar</displayName>
				<unitPattern count="one">{0} bar</unitPattern>
				<unitPattern count="other">{0} bar</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<displayName>mbar</displayName>
				<unitPattern count="one">{0} mbar</unitPattern>
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="pressure-atmosphere">
				<displayName>atm</displayName>
				<unitPattern count="one">{0} atm</unitPattern>
				<unitPattern count="other">{0} atm</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<displayName>hPa</displayName>
				<unitPattern count="one">{0} hPa</unitPattern>
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<displayName>km/h</displayName>
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<displayName>m/s</displayName>
				<unitPattern count="one">{0} m/s</unitPattern>
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<displayName>millas/hora</displayName>
				<unitPattern count="one">{0} mi/h</unitPattern>
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="speed-knot">
				<displayName>nós</displayName>
				<unitPattern count="one">{0} nós</unitPattern>
				<unitPattern count="other">{0} nós</unitPattern>
			</unit>
			<unit type="temperature-generic">
				<displayName>graos</displayName>
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<displayName>°C</displayName>
				<unitPattern count="one">{0} °C</unitPattern>
				<unitPattern count="other">{0} °C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<displayName>°F</displayName>
				<unitPattern count="one">{0} °F</unitPattern>
				<unitPattern count="other">{0} °F</unitPattern>
			</unit>
			<unit type="temperature-kelvin">
				<displayName>K</displayName>
				<unitPattern count="one">{0} K</unitPattern>
				<unitPattern count="other">{0} K</unitPattern>
			</unit>
			<unit type="torque-pound-force-foot">
				<displayName>lbf ft</displayName>
				<unitPattern count="one">{0} lbf ft</unitPattern>
				<unitPattern count="other">{0} lbf ft</unitPattern>
			</unit>
			<unit type="torque-newton-meter">
				<displayName>N⋅m</displayName>
				<unitPattern count="one">{0} N⋅m</unitPattern>
				<unitPattern count="other">{0} N⋅m</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<displayName>km³</displayName>
				<unitPattern count="one">{0} km³</unitPattern>
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-meter">
				<displayName>m³</displayName>
				<unitPattern count="one">{0} m³</unitPattern>
				<unitPattern count="other">{0} m³</unitPattern>
				<perUnitPattern>{0}/m³</perUnitPattern>
			</unit>
			<unit type="volume-cubic-centimeter">
				<displayName>cm³</displayName>
				<unitPattern count="one">{0} cm³</unitPattern>
				<unitPattern count="other">{0} cm³</unitPattern>
				<perUnitPattern>{0}/cm³</perUnitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<displayName>mi³</displayName>
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-cubic-yard">
				<displayName>yd³</displayName>
				<unitPattern count="one">{0} yd³</unitPattern>
				<unitPattern count="other">{0} yd³</unitPattern>
			</unit>
			<unit type="volume-cubic-foot">
				<displayName>ft³</displayName>
				<unitPattern count="one">{0} ft³</unitPattern>
				<unitPattern count="other">{0} ft³</unitPattern>
			</unit>
			<unit type="volume-cubic-inch">
				<displayName>in³</displayName>
				<unitPattern count="one">{0} in³</unitPattern>
				<unitPattern count="other">{0} in³</unitPattern>
			</unit>
			<unit type="volume-megaliter">
				<displayName>Ml</displayName>
				<unitPattern count="one">{0} Ml</unitPattern>
				<unitPattern count="other">{0} Ml</unitPattern>
			</unit>
			<unit type="volume-hectoliter">
				<displayName>hl</displayName>
				<unitPattern count="one">{0} hl</unitPattern>
				<unitPattern count="other">{0} hl</unitPattern>
			</unit>
			<unit type="volume-liter">
				<displayName>litros</displayName>
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
				<perUnitPattern>{0}/l</perUnitPattern>
			</unit>
			<unit type="volume-deciliter">
				<displayName>dl</displayName>
				<unitPattern count="one">{0} dl</unitPattern>
				<unitPattern count="other">{0} dl</unitPattern>
			</unit>
			<unit type="volume-centiliter">
				<displayName>cl</displayName>
				<unitPattern count="one">{0} cl</unitPattern>
				<unitPattern count="other">{0} cl</unitPattern>
			</unit>
			<unit type="volume-milliliter">
				<displayName>ml</displayName>
				<unitPattern count="one">{0} ml</unitPattern>
				<unitPattern count="other">{0} ml</unitPattern>
			</unit>
			<unit type="volume-pint-metric">
				<displayName>ptm</displayName>
				<unitPattern count="one">{0} ptm</unitPattern>
				<unitPattern count="other">{0} ptm</unitPattern>
			</unit>
			<unit type="volume-cup-metric">
				<displayName>cuncas métr.</displayName>
				<unitPattern count="one">{0} mc</unitPattern>
				<unitPattern count="other">{0} mc</unitPattern>
			</unit>
			<unit type="volume-acre-foot">
				<displayName>acre-pés</displayName>
				<unitPattern count="one">{0} ac ft</unitPattern>
				<unitPattern count="other">{0} ac ft</unitPattern>
			</unit>
			<unit type="volume-gallon">
				<displayName>gal EUA</displayName>
				<unitPattern count="one">{0} gal EUA</unitPattern>
				<unitPattern count="other">{0} gal EUA</unitPattern>
				<perUnitPattern>{0}/gal EUA</perUnitPattern>
			</unit>
			<unit type="volume-gallon-imperial">
				<displayName>gal imp.</displayName>
				<unitPattern count="one">{0} gal imp.</unitPattern>
				<unitPattern count="other">{0} gal imp.</unitPattern>
				<perUnitPattern>{0}/gal imp.</perUnitPattern>
			</unit>
			<unit type="volume-quart">
				<displayName>cuartos</displayName>
				<unitPattern count="one">{0} qt</unitPattern>
				<unitPattern count="other">{0} qt</unitPattern>
			</unit>
			<unit type="volume-pint">
				<displayName>pintas</displayName>
				<unitPattern count="one">{0} pt</unitPattern>
				<unitPattern count="other">{0} pt</unitPattern>
			</unit>
			<unit type="volume-cup">
				<displayName>cuncas</displayName>
				<unitPattern count="one">{0} c</unitPattern>
				<unitPattern count="other">{0} c</unitPattern>
			</unit>
			<unit type="volume-fluid-ounce">
				<displayName>fl oz</displayName>
				<unitPattern count="one">{0} fl oz</unitPattern>
				<unitPattern count="other">{0} fl oz</unitPattern>
			</unit>
			<unit type="volume-fluid-ounce-imperial">
				<displayName>fl oz imp.</displayName>
				<unitPattern count="one">{0} fl oz imp.</unitPattern>
				<unitPattern count="other">{0} fl oz imp.</unitPattern>
			</unit>
			<unit type="volume-tablespoon">
				<displayName>cull.</displayName>
				<unitPattern count="one">{0} cull.</unitPattern>
				<unitPattern count="other">{0} cull.</unitPattern>
			</unit>
			<unit type="volume-teaspoon">
				<displayName>cullñs.</displayName>
				<unitPattern count="one">{0} cullña.</unitPattern>
				<unitPattern count="other">{0} cullñs.</unitPattern>
			</unit>
			<unit type="volume-barrel">
				<displayName>barril</displayName>
			</unit>
			<unit type="volume-dessert-spoon">
				<displayName>cull. sobr.</displayName>
				<unitPattern count="one">{0} cull. sobr.</unitPattern>
				<unitPattern count="other">{0} cull. sobr.</unitPattern>
			</unit>
			<unit type="volume-dessert-spoon-imperial">
				<displayName>cull. sobr. imp.</displayName>
				<unitPattern count="one">{0} cull. sobr. imp.</unitPattern>
				<unitPattern count="other">{0} cull. sobr. imp.</unitPattern>
			</unit>
			<unit type="volume-drop">
				<displayName>gota</displayName>
				<unitPattern count="one">{0} gota</unitPattern>
				<unitPattern count="other">{0} gotas</unitPattern>
			</unit>
			<unit type="volume-dram">
				<displayName>dracma</displayName>
				<unitPattern count="one">{0} dracma</unitPattern>
				<unitPattern count="other">{0} dracmas</unitPattern>
			</unit>
			<unit type="volume-jigger">
				<displayName>medidor</displayName>
				<unitPattern count="one">{0} medidor</unitPattern>
				<unitPattern count="other">{0} medidores</unitPattern>
			</unit>
			<unit type="volume-pinch">
				<displayName>chisco</displayName>
				<unitPattern count="one">{0} chisco</unitPattern>
				<unitPattern count="other">{0} chiscos</unitPattern>
			</unit>
			<unit type="volume-quart-imperial">
				<displayName>cuarto imperial</displayName>
				<unitPattern count="one">{0} cto. imp.</unitPattern>
				<unitPattern count="other">{0} ctos. imp.</unitPattern>
			</unit>
			<coordinateUnit>
				<displayName>punto</displayName>
				<coordinateUnitPattern type="east">{0} L</coordinateUnitPattern>
				<coordinateUnitPattern type="north">{0} N</coordinateUnitPattern>
				<coordinateUnitPattern type="south">{0} S</coordinateUnitPattern>
				<coordinateUnitPattern type="west">{0} O</coordinateUnitPattern>
			</coordinateUnit>
		</unitLength>
		<unitLength type="narrow">
			<compoundUnit type="10p-1">
				<unitPrefixPattern>d{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-2">
				<unitPrefixPattern>c{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-3">
				<unitPrefixPattern>m{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-6">
				<unitPrefixPattern>μ{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-9">
				<unitPrefixPattern>n{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-12">
				<unitPrefixPattern>p{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-15">
				<unitPrefixPattern>f{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-18">
				<unitPrefixPattern>a{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-21">
				<unitPrefixPattern>z{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p-24">
				<unitPrefixPattern>y{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p1">
				<unitPrefixPattern>da{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p2">
				<unitPrefixPattern>h{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p3">
				<unitPrefixPattern>k{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p6">
				<unitPrefixPattern>M{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p9">
				<unitPrefixPattern>G{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p12">
				<unitPrefixPattern>T{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p15">
				<unitPrefixPattern>P{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p18">
				<unitPrefixPattern>E{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p21">
				<unitPrefixPattern>Z{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="10p24">
				<unitPrefixPattern>Y{0}</unitPrefixPattern>
			</compoundUnit>
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<compoundUnit type="power2">
				<compoundUnitPattern1>{0}²</compoundUnitPattern1>
			</compoundUnit>
			<compoundUnit type="power3">
				<compoundUnitPattern1>{0}³</compoundUnitPattern1>
			</compoundUnit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="concentr-percent">
				<displayName>%</displayName>
				<unitPattern count="one">{0} %</unitPattern>
				<unitPattern count="other">{0} %</unitPattern>
			</unit>
			<unit type="consumption-liter-per-100-kilometer">
				<displayName>l/100 km</displayName>
				<unitPattern count="one">{0} l/100 km</unitPattern>
				<unitPattern count="other">{0} l/100 km</unitPattern>
			</unit>
			<unit type="duration-year">
				<displayName>a.</displayName>
				<unitPattern count="one">{0} a.</unitPattern>
				<unitPattern count="other">{0} a.</unitPattern>
			</unit>
			<unit type="duration-month">
				<displayName>mes</displayName>
				<unitPattern count="one">{0} m.</unitPattern>
				<unitPattern count="other">{0} m.</unitPattern>
			</unit>
			<unit type="duration-week">
				<displayName>sem.</displayName>
				<unitPattern count="one">{0} sem.</unitPattern>
				<unitPattern count="other">{0} sem.</unitPattern>
			</unit>
			<unit type="duration-day">
				<displayName>día</displayName>
				<unitPattern count="one">{0} d</unitPattern>
				<unitPattern count="other">{0} d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<displayName>h</displayName>
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-minute">
				<displayName>min</displayName>
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-second">
				<displayName>s</displayName>
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<displayName>ms</displayName>
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<displayName>km</displayName>
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-meter">
				<displayName>m</displayName>
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<displayName>cm</displayName>
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<displayName>mm</displayName>
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<displayName>kg</displayName>
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-gram">
				<displayName>g</displayName>
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<displayName>km/h</displayName>
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<displayName>°C</displayName>
				<unitPattern count="one">{0} °C</unitPattern>
				<unitPattern count="other">{0} °C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-liter">
				<displayName>l</displayName>
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
			<coordinateUnit>
				<displayName>punto</displayName>
				<coordinateUnitPattern type="east">{0} L</coordinateUnitPattern>
				<coordinateUnitPattern type="north">{0} N</coordinateUnitPattern>
				<coordinateUnitPattern type="south">{0} S</coordinateUnitPattern>
				<coordinateUnitPattern type="west">{0} O</coordinateUnitPattern>
			</coordinateUnit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="or">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} ou {1}</listPatternPart>
			<listPatternPart type="2">{0} ou {1}</listPatternPart>
		</listPattern>
		<listPattern type="standard-narrow">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="standard-short">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-narrow">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-short">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>si:s</yesstr>
			<nostr>non:n</nostr>
		</messages>
	</posix>
	<characterLabels>
		<characterLabelPattern type="all">{0} (todo)</characterLabelPattern>
		<characterLabelPattern type="category-list">{0}: {1}</characterLabelPattern>
		<characterLabelPattern type="compatibility">{0} (compatibilidade)</characterLabelPattern>
		<characterLabelPattern type="enclosed">{0} (incluíd.)</characterLabelPattern>
		<characterLabelPattern type="extended">{0} (ampliad.)</characterLabelPattern>
		<characterLabelPattern type="historic">{0} (históric.)</characterLabelPattern>
		<characterLabelPattern type="miscellaneous">{0} (variad.)</characterLabelPattern>
		<characterLabelPattern type="other">{0} (outr.)</characterLabelPattern>
		<characterLabelPattern type="scripts">sistemas de escrita ({0})</characterLabelPattern>
		<characterLabelPattern type="strokes" count="one">{0} trazo</characterLabelPattern>
		<characterLabelPattern type="strokes" count="other">{0} trazos</characterLabelPattern>
		<characterLabelPattern type="subscript">{0} subíndice</characterLabelPattern>
		<characterLabelPattern type="superscript">{0} superíndice</characterLabelPattern>
		<characterLabel type="activities">actividades</characterLabel>
		<characterLabel type="african_scripts">sistemas de escrita africanos</characterLabel>
		<characterLabel type="american_scripts">sistemas de escrita americanos</characterLabel>
		<characterLabel type="animal">animais</characterLabel>
		<characterLabel type="animals_nature">animais e natureza</characterLabel>
		<characterLabel type="arrows">frechas</characterLabel>
		<characterLabel type="body">corpo</characterLabel>
		<characterLabel type="box_drawing">deseño de caixas</characterLabel>
		<characterLabel type="braille">braille</characterLabel>
		<characterLabel type="building">edificios</characterLabel>
		<characterLabel type="bullets_stars">viñetas/estrelas</characterLabel>
		<characterLabel type="consonantal_jamo">jamos consonánticos</characterLabel>
		<characterLabel type="currency_symbols">símbolos de moedas</characterLabel>
		<characterLabel type="dash_connector">trazos/conectores</characterLabel>
		<characterLabel type="digits">díxitos</characterLabel>
		<characterLabel type="dingbats">pictogramas</characterLabel>
		<characterLabel type="divination_symbols">símbolos astrolóxicos</characterLabel>
		<characterLabel type="downwards_arrows">frechas cara arriba</characterLabel>
		<characterLabel type="downwards_upwards_arrows">frechas cara abaixo</characterLabel>
		<characterLabel type="east_asian_scripts">sistemas de escrita de Asia Oriental</characterLabel>
		<characterLabel type="emoji">emojis</characterLabel>
		<characterLabel type="european_scripts">sistemas de escrita europeos</characterLabel>
		<characterLabel type="female">mulleres</characterLabel>
		<characterLabel type="flag">bandeira</characterLabel>
		<characterLabel type="flags">bandeiras</characterLabel>
		<characterLabel type="food_drink">comida e bebida</characterLabel>
		<characterLabel type="format">formato</characterLabel>
		<characterLabel type="format_whitespace">formato e espazos en branco</characterLabel>
		<characterLabel type="full_width_form_variant">variacións de forma de ancho completo</characterLabel>
		<characterLabel type="geometric_shapes">figuras xeométricas</characterLabel>
		<characterLabel type="half_width_form_variant">variacións de forma de ancho medio</characterLabel>
		<characterLabel type="han_characters">caracteres han</characterLabel>
		<characterLabel type="han_radicals">radicais han</characterLabel>
		<characterLabel type="hanja">hanja</characterLabel>
		<characterLabel type="hanzi_simplified">hanzi (simplificado)</characterLabel>
		<characterLabel type="hanzi_traditional">hanzi (tradicional)</characterLabel>
		<characterLabel type="heart">corazóns</characterLabel>
		<characterLabel type="historic_scripts">sistemas de escrita históricos</characterLabel>
		<characterLabel type="ideographic_desc_characters">caracteres de descrición ideográfica</characterLabel>
		<characterLabel type="japanese_kana">kanas xaponeses</characterLabel>
		<characterLabel type="kanbun">kanbun</characterLabel>
		<characterLabel type="kanji">kanjis</characterLabel>
		<characterLabel type="keycap">tecla</characterLabel>
		<characterLabel type="leftwards_arrows">frechas á esquerda</characterLabel>
		<characterLabel type="leftwards_rightwards_arrows">frechas á esquerda e dereita</characterLabel>
		<characterLabel type="letterlike_symbols">símbolos con letras</characterLabel>
		<characterLabel type="limited_use">uso limitado</characterLabel>
		<characterLabel type="male">homes</characterLabel>
		<characterLabel type="math_symbols">símbolos matemáticos</characterLabel>
		<characterLabel type="middle_eastern_scripts">sistemas de escritura de Oriente Medio</characterLabel>
		<characterLabel type="miscellaneous">varios</characterLabel>
		<characterLabel type="modern_scripts">sistemas de escritura modernos</characterLabel>
		<characterLabel type="modifier">modificadores</characterLabel>
		<characterLabel type="musical_symbols">símbolos musicais</characterLabel>
		<characterLabel type="nature">natureza</characterLabel>
		<characterLabel type="nonspacing">sen espazo</characterLabel>
		<characterLabel type="numbers">números</characterLabel>
		<characterLabel type="objects">obxectos</characterLabel>
		<characterLabel type="other">outros</characterLabel>
		<characterLabel type="paired">emparellados</characterLabel>
		<characterLabel type="person">persoas</characterLabel>
		<characterLabel type="phonetic_alphabet">alfabeto fonético</characterLabel>
		<characterLabel type="pictographs">pictogramas</characterLabel>
		<characterLabel type="place">lugares</characterLabel>
		<characterLabel type="plant">plantas</characterLabel>
		<characterLabel type="punctuation">puntuación</characterLabel>
		<characterLabel type="rightwards_arrows">frechas á dereita</characterLabel>
		<characterLabel type="sign_standard_symbols">signos/símbolos estándar</characterLabel>
		<characterLabel type="small_form_variant">pequenas variantes</characterLabel>
		<characterLabel type="smiley">emoticonas</characterLabel>
		<characterLabel type="smileys_people">emoticonas e persoas</characterLabel>
		<characterLabel type="south_asian_scripts">sistemas de escrita de Asia Meridional</characterLabel>
		<characterLabel type="southeast_asian_scripts">sistemas de escrita do Sueste asiático</characterLabel>
		<characterLabel type="spacing">espazamento</characterLabel>
		<characterLabel type="sport">deporte</characterLabel>
		<characterLabel type="symbols">símbolos</characterLabel>
		<characterLabel type="technical_symbols">símbolos técnicos</characterLabel>
		<characterLabel type="tone_marks">marcas tonais</characterLabel>
		<characterLabel type="travel">viaxar</characterLabel>
		<characterLabel type="travel_places">viaxar/lugares</characterLabel>
		<characterLabel type="upwards_arrows">frechas cara arriba</characterLabel>
		<characterLabel type="variant_forms">variantes</characterLabel>
		<characterLabel type="vocalic_jamo">jamos vocálicos</characterLabel>
		<characterLabel type="weather">meteoroloxía</characterLabel>
		<characterLabel type="western_asian_scripts">sistemas de escrita de Asia Occidental</characterLabel>
		<characterLabel type="whitespace">espazos en branco</characterLabel>
	</characterLabels>
	<typographicNames>
		<axisName type="ital">cursiva</axisName>
		<axisName type="opsz">tamaño óptico</axisName>
		<axisName type="slnt">inclinación</axisName>
		<axisName type="wdth">ancho</axisName>
		<axisName type="wght">grosor</axisName>
		<styleName type="ital" subtype="1">cursiva</styleName>
		<styleName type="opsz" subtype="8">lenda</styleName>
		<styleName type="opsz" subtype="12">texto</styleName>
		<styleName type="opsz" subtype="18">título</styleName>
		<styleName type="opsz" subtype="72">pantalla</styleName>
		<styleName type="opsz" subtype="144">cartel</styleName>
		<styleName type="slnt" subtype="-12">inclinada inversa</styleName>
		<styleName type="slnt" subtype="0">recta</styleName>
		<styleName type="slnt" subtype="12">inclinada</styleName>
		<styleName type="slnt" subtype="24">extrainclinada</styleName>
		<styleName type="wdth" subtype="50">ultracondensada</styleName>
		<styleName type="wdth" subtype="50" alt="compressed">ultracomprimida</styleName>
		<styleName type="wdth" subtype="50" alt="narrow">ultraestrecha</styleName>
		<styleName type="wdth" subtype="62.5">extracondensada</styleName>
		<styleName type="wdth" subtype="62.5" alt="compressed">extracomprimida</styleName>
		<styleName type="wdth" subtype="62.5" alt="narrow">extraestreita</styleName>
		<styleName type="wdth" subtype="75">condensada</styleName>
		<styleName type="wdth" subtype="75" alt="compressed">comprimida</styleName>
		<styleName type="wdth" subtype="75" alt="narrow">estreita</styleName>
		<styleName type="wdth" subtype="87.5">semicondensada</styleName>
		<styleName type="wdth" subtype="87.5" alt="compressed">semicomprimida</styleName>
		<styleName type="wdth" subtype="87.5" alt="narrow">semiestreita</styleName>
		<styleName type="wdth" subtype="100">normal</styleName>
		<styleName type="wdth" subtype="112.5">semiexpandida</styleName>
		<styleName type="wdth" subtype="112.5" alt="extended">semiestendida</styleName>
		<styleName type="wdth" subtype="112.5" alt="wide">semiancha</styleName>
		<styleName type="wdth" subtype="125">expandida</styleName>
		<styleName type="wdth" subtype="125" alt="extended">estendida</styleName>
		<styleName type="wdth" subtype="125" alt="wide">ancha</styleName>
		<styleName type="wdth" subtype="150">extraexpandida</styleName>
		<styleName type="wdth" subtype="150" alt="extended">extraestendida</styleName>
		<styleName type="wdth" subtype="150" alt="wide">extraancha</styleName>
		<styleName type="wdth" subtype="200">ultraexpandida</styleName>
		<styleName type="wdth" subtype="200" alt="extended">ultraestendida</styleName>
		<styleName type="wdth" subtype="200" alt="wide">ultraancha</styleName>
		<styleName type="wght" subtype="100">tenue</styleName>
		<styleName type="wght" subtype="200">extrafina</styleName>
		<styleName type="wght" subtype="200" alt="ultra">ultrafina</styleName>
		<styleName type="wght" subtype="300">fina</styleName>
		<styleName type="wght" subtype="350">semifina</styleName>
		<styleName type="wght" subtype="380">libro</styleName>
		<styleName type="wght" subtype="400">redonda</styleName>
		<styleName type="wght" subtype="500">media</styleName>
		<styleName type="wght" subtype="600">semigrosa</styleName>
		<styleName type="wght" subtype="600" alt="demi">semigrosa</styleName>
		<styleName type="wght" subtype="700">grosa</styleName>
		<styleName type="wght" subtype="800">extragrosa</styleName>
		<styleName type="wght" subtype="900">negra</styleName>
		<styleName type="wght" subtype="900" alt="heavy">densa</styleName>
		<styleName type="wght" subtype="950">extranegra</styleName>
		<styleName type="wght" subtype="950" alt="ultrablack">ultranegra</styleName>
		<styleName type="wght" subtype="950" alt="ultraheavy">ultradensa</styleName>
		<featureName type="afrc">fraccións verticais</featureName>
		<featureName type="cpsp">espazamento entre maiúsculas</featureName>
		<featureName type="dlig">ligaduras opcionais</featureName>
		<featureName type="frac">fraccións diagonais</featureName>
		<featureName type="lnum">números aliñados</featureName>
		<featureName type="onum">cifras en estilo antigo</featureName>
		<featureName type="ordn">ordinais</featureName>
		<featureName type="pnum">números proporcionais</featureName>
		<featureName type="smcp">versaleta</featureName>
		<featureName type="tnum">números tabulares</featureName>
		<featureName type="zero">cero cruzado</featureName>
	</typographicNames>
</ldml>
