grant {
    permission java.io.FilePermission "<<ALL FILES>>", "read";

    permission java.util.PropertyPermission "os.name", "read";
    permission java.util.PropertyPermission "os.arch", "read";
    permission java.util.PropertyPermission "test.root", "read";
    permission java.util.PropertyPermission "jdk.openssl.cryptoLibPath", "read,write";
    permission java.util.PropertyPermission "sm4.native.disable", "read,write";

    permission java.lang.RuntimePermission "accessDeclaredMembers";
    permission java.lang.reflect.ReflectPermission "suppressAccessChecks";
    permission java.lang.RuntimePermission "accessClassInPackage.sun.security.util";
    permission java.lang.RuntimePermission "accessClassInPackage.sun.security.provider";
    permission java.lang.RuntimePermission "accessClassInPackage.com.sun.crypto.provider";

    permission java.security.SecurityPermission "getProperty.*";
};
