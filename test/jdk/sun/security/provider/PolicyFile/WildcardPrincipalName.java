/*
 * Copyright (c) 2013, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

/*
 * @test
 * @bug 8008908
 * @summary wildcard principal names are not processed correctly
 * @run main/othervm/policy=wildcard.policy WildcardPrincipalName
 */

import java.security.AccessController;
import java.security.Permission;
import java.security.Principal;
import java.security.PrivilegedAction;
import java.util.HashSet;
import java.util.PropertyPermission;
import java.util.Set;
import javax.security.auth.Subject;
import javax.security.auth.x500.X500Principal;

public class WildcardPrincipalName {

    public static void main(String[] args) throws Exception {

        X500Principal duke = new X500Principal("CN=Duke");
        PropertyPermission pp = new PropertyPermission("user.home", "read");
        RunAsPrivilegedUserAction runAsPrivilegedUserAction
            = new RunAsPrivilegedUserAction(duke,
                                            new CheckPermissionAction(pp));
        AccessController.doPrivileged(runAsPrivilegedUserAction);
        System.out.println("test PASSED");
    }

    private static class RunAsPrivilegedUserAction
        implements PrivilegedAction<Void> {
        private final PrivilegedAction<Void> action;
        private final Principal principal;

        RunAsPrivilegedUserAction(Principal principal,
                                  PrivilegedAction<Void> action) {
            this.principal = principal;
            this.action = action;
        }

        @Override public Void run() {
            Set<Principal> principals = new HashSet<>();
            Set<Object> publicCredentials = new HashSet<>();
            Set<Object> privateCredentials = new HashSet<>();

            principals.add(principal);
            Subject subject = new Subject(true,
                                          principals,
                                          publicCredentials,
                                          privateCredentials);

            Subject.doAsPrivileged(subject, action, null);
            return null;
        }
    }

    private static class CheckPermissionAction
        implements PrivilegedAction<Void> {
        private final Permission permission;

        CheckPermissionAction(Permission permission) {
            this.permission = permission;
        }

        @Override public Void run() {
            AccessController.checkPermission(permission);
            return null;
        }
    }
}
