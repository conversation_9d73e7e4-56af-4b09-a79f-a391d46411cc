/*
 * Copyright (c) 2001, 2023, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

/*
 * @test
 * @bug 4148154
 * @summary Tests that menu items created by JMenu.add(Action) method
           have right HorizontalTextPosition.
 * @run main bug4148154
 */

import java.awt.event.ActionEvent;
import javax.swing.AbstractAction;
import javax.swing.JMenu;
import javax.swing.JMenuItem;

public class bug4148154
{
    public static void main(String[] args) {
        JMenu menu = new JMenu();
        JMenuItem mi = menu.add(new AbstractAction() {
                public void actionPerformed(ActionEvent ev) {}
            });
        if (mi.getHorizontalTextPosition() != JMenu.LEADING &&
            mi.getHorizontalTextPosition() != JMenu.TRAILING) {

            throw new RuntimeException("Failed:");
        }
    }
}
