
grant {
    permission java.security.SecurityPermission
			"createPolicy.JavaPolicy";
    permission java.security.SecurityPermission
			"createPolicy.FooPolicy";
    permission java.security.SecurityPermission
			"createPolicy.GetInstancePolicySpi";
    permission java.util.PropertyPermission
			"test.src", "read";
    permission java.lang.RuntimePermission
			"accessClassInPackage.sun.net.www";
    permission java.io.FilePermission
			"${test.src}/GetInstance.policyURL",
			"read";
    permission java.lang.RuntimePermission
			"accessClassInPackage.sun.security.provider";
    permission java.lang.SecurityPermission
			"setSecurityManager";
    permission java.lang.RuntimePermission
			"getProtectionDomain";
    permission java.security.SecurityPermission
			"setPolicy";
    permission java.security.SecurityPermission
			"putProviderProperty.GetInstanceProvider";
    permission java.security.SecurityPermission
			"GetInstanceTest";
};
