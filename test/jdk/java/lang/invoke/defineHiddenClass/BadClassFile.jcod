/*
 * Copyright (c) 2020, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

// this_class is modified to CONSTANT_NameAndType_info
class BadClassFile {
  0xCAFEBABE;
  0; // minor version
  58; // version
  [] { // Constant Pool
    ; // first element is empty
    Method #2 #3; // #1
    class #4; // #2
    NameAndType #5 #6; // #3
    Utf8 "java/lang/Object"; // #4
    Utf8 "<init>"; // #5
    Utf8 "()V"; // #6
    NameAndType #5 #6; // #7
    Utf8 "BadClassFile"; // #8
    Utf8 "Code"; // #9
    Utf8 "LineNumberTable"; // #10
    Utf8 "SourceFile"; // #11
    Utf8 "BadClassFile.java"; // #12
  } // Constant Pool

  0x0021; // access
  #7;// this_cpx
  #2;// super_cpx

  [] { // Interfaces
  } // Interfaces

  [] { // fields
  } // fields

  [] { // methods
    { // Member
      0x0001; // access
      #5; // name_cpx
      #6; // sig_cpx
      [] { // Attributes
        Attr(#9) { // Code
          1; // max_stack
          1; // max_locals
          Bytes[]{
            0x2AB70001B1;
          }
          [] { // Traps
          } // end Traps
          [] { // Attributes
            Attr(#10) { // LineNumberTable
              [] { // LineNumberTable
                0  28;
              }
            } // end LineNumberTable
          } // Attributes
        } // end Code
      } // Attributes
    } // Member
  } // methods

  [] { // Attributes
    Attr(#11) { // SourceFile
      #12;
    } // end SourceFile
  } // Attributes
} // end class BadClassFile
