/*
 * Copyright (c) 1999, 2019, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

/* @test
 * @bug 4180735
 * @clean Foo GetField
 * @build GetField
 * @run main GetField
 *
 * @summary Make sure that getField() of ObjectStreamClass works correctly for
 *          object types.
 */
import java.io.*;

public class GetField implements Serializable{
    private static final long serialVersionUID = 1L;

    String str;
    int i;

    public static void main(String[] args) throws Exception {
        ObjectStreamClass cl = ObjectStreamClass.lookup(GetField.class);
        if (cl == null)
            throw new RuntimeException("Cannot resolve class : GetField");

        if (cl.getField("str") == null)
            throw new RuntimeException(
                "ObjectStreamClass.getField() failed for object type");

        if (cl.getField("i") == null)
            throw new RuntimeException(
                "ObjectStreamClass.getField() failed for primitive type");
    }
}
