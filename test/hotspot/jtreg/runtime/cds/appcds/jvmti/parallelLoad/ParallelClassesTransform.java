/*
 * Copyright (c) 2016, 2019, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 *
 */

class ParallelClassTr0  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr1  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr2  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr3  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr4  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr5  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr6  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr7  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr8  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr9  { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr10 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr11 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr12 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr13 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr14 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr15 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr16 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr17 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr18 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr19 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr20 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr21 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr22 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr23 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr24 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr25 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr26 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr27 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr28 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr29 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr30 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr31 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr32 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr33 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr34 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr35 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr36 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr37 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr38 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }
class ParallelClassTr39 { public static String testString = ParallelClassesTransform.BEFORE_PATTERN; }

class ParallelClassesTransform {
    public static final int NUMBER_OF_CLASSES = 40;
    public static final String BEFORE_PATTERN = "class-transform-check: this-should-be-transformed";
    public static final String AFTER_PATTERN =  "class-transform-check: this-has-been--transformed";
}
