/*
 * Copyright (c) 2016, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 *
 */

// jcod file for UseBadInterface1.java
// class UseBadInterface1 implements p1.BadInterface1 {
//     int i;
//     UseBadInterface1() {}
//     public static void main(java.lang.String[] unused) { }
// }

class UseBadInterface1 {
  0xCAFEBABE;
  0; // minor version
  53; // version
  [] { // Constant Pool
    ; // first element is empty
    Method #3 #10; // #1    
    Utf8 "UseBadInterface1.java"; // #2    
    class #4; // #3    
    Utf8 "java/lang/Object"; // #4    
    class #8; // #5    
    Utf8 "([Ljava/lang/String;)V"; // #6    
    class #11; // #7    
    Utf8 "UseBadInterface1"; // #8    
    Utf8 "main"; // #9    
    NameAndType #17 #13; // #10    
    Utf8 "p1//BadInterface1"; // #11    
    Utf8 "SourceFile"; // #12    
    Utf8 "()V"; // #13    
    Utf8 "I"; // #14    
    Utf8 "Code"; // #15    
    Utf8 "i"; // #16    
    Utf8 "<init>"; // #17    
  } // Constant Pool

  0x0020; // access
  #5;// this_cpx
  #3;// super_cpx

  [] { // Interfaces
    #7;
  } // Interfaces

  [] { // fields
    { // Member
      0x0000; // access
      #16; // name_cpx
      #14; // sig_cpx
      [] { // Attributes
      } // Attributes
    } // Member
  } // fields

  [] { // methods
    { // Member
      0x0000; // access
      #17; // name_cpx
      #13; // sig_cpx
      [] { // Attributes
        Attr(#15) { // Code
          1; // max_stack
          1; // max_locals
          Bytes[]{
            0x2AB70001B1;
          };
          [] { // Traps
          } // end Traps
          [] { // Attributes
          } // Attributes
        } // end Code
      } // Attributes
    } // Member
    ;
    { // Member
      0x0009; // access
      #9; // name_cpx
      #6; // sig_cpx
      [] { // Attributes
        Attr(#15) { // Code
          0; // max_stack
          1; // max_locals
          Bytes[]{
            0xB1;
          };
          [] { // Traps
          } // end Traps
          [] { // Attributes
          } // Attributes
        } // end Code
      } // Attributes
    } // Member
  } // methods

  [] { // Attributes
    Attr(#12) { // SourceFile
      #2;
    } // end SourceFile
  } // Attributes
} // end class UseBadInterface1
