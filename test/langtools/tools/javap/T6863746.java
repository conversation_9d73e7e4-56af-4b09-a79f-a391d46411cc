/*
 * Copyright (c) 2009, 2016, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

import java.io.*;

/*
 * @test
 * @bug 6863746
 * @summary javap should not scan ct.sym by default
 * @modules jdk.jdeps/com.sun.tools.javap
 */

public class ******** {
    public static void main(String... args) throws Exception{
        new ********().run();
    }

    public void run() throws Exception {
        String[] args = { "-c", "java.lang.Object" };
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        int rc = com.sun.tools.javap.Main.run(args, pw);
        pw.close();
        String out = sw.toString();
        System.out.println(out);
        String[] lines = out.split("\n");
        // If ct.sym is being read, the output does not include
        // Code attributes, so check for Code attributes as a
        // way of detecting that ct.sym is not being used.
        if (lines.length < 50 || out.indexOf("Code:") == -1)
            throw new Exception("unexpected output from javap");
    }
}
