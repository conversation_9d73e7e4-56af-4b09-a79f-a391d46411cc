/*
 * Copyright (c) 2013, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

import java.io.*;

interface IVariableAssignmentTarget extends Serializable {
    String get();
}

class TESTVariableAssignmentTarget {

    public TESTVariableAssignmentTarget() {
    }

    public void write(ObjectOutput out) throws IOException {
        IVariableAssignmentTarget res1 = () -> "fu";
        IVariableAssignmentTarget res2 = () -> "bar";
        out.writeObject(res1);
        out.writeObject(res2);
    }

    public void readCheck(ObjectInput in) throws Exception {
        IVariableAssignmentTarget lam = (IVariableAssignmentTarget) in.readObject();
        Object val = lam.get();
        if (!val.equals("fu")) {
            throw new IllegalArgumentException("Expected 'fu'");
        }
        lam = (IVariableAssignmentTarget) in.readObject();
        val = lam.get();
        if (!val.equals("bar")) {
            throw new IllegalArgumentException("Expected 'bar'");
        }
    }
}
