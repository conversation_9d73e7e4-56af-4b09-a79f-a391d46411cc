/*
 * Copyright (c) 1999, 2001, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */

/*
 * @test
 * @bug 4283170
 * @summary Verify that access to member of java.lang.Object via qualification of
 * object of an interface type is handled successfully.
 * <AUTHOR>
 */

public class ObjectMethodRefFromInterface {

    public interface JunkInterface {
        public void blahBlahBlah(int j);
    }

    public static class JunkClass implements JunkInterface {
        public void blahBlahBlah(int j) {
            return;
        }
    }

    public void doReference(JunkInterface o) {
        Class c = o.getClass();
    }

    public static void main(String args[]) {
        JunkInterface o = new JunkClass();
        ObjectMethodRefFromInterface cnt = new ObjectMethodRefFromInterface();
        cnt.doReference(o);
    }
}
