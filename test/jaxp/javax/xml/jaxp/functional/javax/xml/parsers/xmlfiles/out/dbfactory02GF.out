startDocument...
startPrefixMapping...
prefix: <ar> uri: <http://www.xml.com/books>
startPrefixMapping...
prefix: <br> uri: <http://www.abc.com>
startElement...
namespaceURI: <> localName: <note> qName: <note> Number of Attributes: <2> Line# <0>
characters...length is:1
<
>
startElement...
namespaceURI: <> localName: <to> qName: <to> Number of Attributes: <0> Line# <0>
startElement...
namespaceURI: <> localName: <sender> qName: <sender> Number of Attributes: <0> Line# <0>
characters...length is:4
<John>
endElement...
namespaceURI: <> localName: <sender> qName: <sender>
endElement...
namespaceURI: <> localName: <to> qName: <to>
characters...length is:1
<
>
startElement...
namespaceURI: <> localName: <with> qName: <with> Number of Attributes: <0> Line# <0>
startElement...
namespaceURI: <> localName: <message> qName: <message> Number of Attributes: <0> Line# <0>
characters...length is:15
<with whitespace>
endElement...
namespaceURI: <> localName: <message> qName: <message>
endElement...
namespaceURI: <> localName: <with> qName: <with>
characters...length is:1
<
>
startElement...
namespaceURI: <http://www.xml.com/books> localName: <from> qName: <ar:from> Number of Attributes: <0> Line# <0>
startElement...
namespaceURI: <> localName: <tab> qName: <tab> Number of Attributes: <0> Line# <0>
characters...length is:4
<Jani>
endElement...
namespaceURI: <> localName: <tab> qName: <tab>
endElement...
namespaceURI: <http://www.xml.com/books> localName: <from> qName: <ar:from>
characters...length is:1
<
>
startElement...
namespaceURI: <http://www.abc.com> localName: <from> qName: <br:from> Number of Attributes: <0> Line# <0>
characters...length is:4
<Next>
endElement...
namespaceURI: <http://www.abc.com> localName: <from> qName: <br:from>
characters...length is:1
<
>
startElement...
namespaceURI: <> localName: <heading> qName: <heading> Number of Attributes: <0> Line# <0>
characters...length is:8
<Reminder>
endElement...
namespaceURI: <> localName: <heading> qName: <heading>
characters...length is:1
<
>
startElement...
namespaceURI: <> localName: <body> qName: <body> Number of Attributes: <0> Line# <0>
characters...length is:9
< weekend!>
endElement...
namespaceURI: <> localName: <body> qName: <body>
characters...length is:1
<
>
endElement...
namespaceURI: <> localName: <note> qName: <note>
endPrefixMapping...
prefix: <br>
endPrefixMapping...
prefix: <ar>
endDocument...
