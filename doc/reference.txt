上下文提示词

本次任务为为腾讯犀牛鸟开源活动实现一个issue，内容为Implement a new SM4Cipher with JIN and OpenSSL 3.5.0.
但是与issue内容不同的是在review的过程中导师提出“试图完备地实现CipherSpi，可能比较复杂。能否考虑只是提供一个Native SM4Engine？”，于是我后面主要实现了一个SM4EngineNative.java和sm4_engine_native.c，并修改了SM4Crypt.java文件。

目前不要复用CTX，不要做批量处理的优化，我只需要在底层一次性操作不出错就行

目前在修改完善阶段，在按照导师的要求“我还是建议，目前仅提供一个Native SM4 Engine即可。先不要修改操作模式的实现，尽量少修改代码。先看看这个方案对性能的影响有多大”，这个要求已经实现了，不过有性能问题，原因是逐块处理，我在feature/issue-8-backup-1分支里通过修改四个模式类实现了批量处理，可能还有其他方案，现在先不管

当前的设计如下：
       Java实现的设计是：
       SM4Engine：纯算法，只做 16 字节块的加密/解密
       模式类：处理所有模式特定的逻辑（IV、计数器、认证等）

       Java实现的调用链是：
       ECB模式：ElectronicCodeBook → SM4Crypt.encryptBlock/decryptBlock → SM4Engine.processBlock
       CBC模式：CipherBlockChaining → SM4Crypt.encryptBlock/decryptBlock → SM4Engine.processBlock
       CTR模式：CounterMode → SM4Crypt.encryptBlock/decryptBlock → SM4Engine.processBlock
       GCM模式：GaloisCounterMode → GCTR → CounterMode → SM4Crypt.encryptBlock/decryptBlock → SM4Engine.processBlock
       native实现的调用链类似。

       如果想仅提供一个SM4EngineNative的话，想到的就是在SM4Crypt里通过判断native环境是否可用来分支到SM4EngineNative里再通过JNI调用原生的加密/解密。重新理解了下，传递给 SM4Crypt.encryptBlock/decryptBlock 的数据，本质上都是需要做 ECB 加密的数据：
       CBC：传入的是 plain XOR IV，需要 ECB 加密
       CTR：传入的是 counter，需要 ECB 加密
       GCM：传入的也是 counter，需要 ECB 加密
       ECB：传入的是 plain，需要 ECB 加密

       所以如果我的理解是对的话，确实有最小的实现方案，只在SM4EngineNative通过JNI实现通用的加解密。

SM4Bench.java文件是专门用来测试这个实现的，相关经过验证的编译、测试方法和数据在doc/SM4_NATIVE_ENGINE_REPRODUCTION_GUIDE.md中

请你在处理openssl相关代码的时候尽量参考doc/evp.txt、doc/evp_sm4_cbc.txt、doc/evp_cipher_sm4.txt、doc/EVP_CIPHER_meth_new.txt、doc/EVP_EncryptInit.txt这些文件的内容，这些是openssl3.5版本的官方文档相关内容，确保符合规范。

如果有需要你可以使用fetch工具查看相关网页
issue链接：https://cnb.cool/tencent/TencentKona/TencentKona-17/-/issues/8
pr链接：https://cnb.cool/tencent/TencentKona/TencentKona-17/-/pulls/14
pr中的文件diff链接：https://cnb.cool/tencent/TencentKona/TencentKona-17/-/pulls/14/diff